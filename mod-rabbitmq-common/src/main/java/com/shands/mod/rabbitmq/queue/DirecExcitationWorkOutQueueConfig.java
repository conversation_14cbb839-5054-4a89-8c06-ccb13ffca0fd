package com.shands.mod.rabbitmq.queue;

import com.shands.mod.rabbitmq.constant.QualityMqConstant;
import java.util.HashMap;
import java.util.Map;
import javax.annotation.Resource;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.CustomExchange;
import org.springframework.amqp.core.Exchange;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


@Configuration
public class DirecExcitationWorkOutQueueConfig {

  @Resource
  private RabbitAdmin rabbitAdmin;

  @Bean
  public void directExcitationWorkOutRabbitConfigInit() {
    rabbitAdmin.declareExchange(delayWorkOutExchage());
    rabbitAdmin.declareQueue(delayWorkOutQueue());
  }

  @Bean
  CustomExchange delayWorkOutExchage() {
    Map<String, Object> args = new HashMap<>();
    args.put("x-delayed-type", "direct");
    return new CustomExchange(QualityMqConstant.EXCHANGE_WORK_PUSH, "x-delayed-message",
        true, false, args);
  }

  @Bean
  Queue delayWorkOutQueue() {
    return new Queue(QualityMqConstant.QUEUE_WORK_PUSH);
  }

  @Bean
  Binding delayWorkOutBinding(Queue delayWorkOutQueue,
      Exchange delayWorkOutExchage) {
    return BindingBuilder.bind(delayWorkOutQueue).to(delayWorkOutExchage)
        .with(QualityMqConstant.WORK_PUSH_RULE).noargs();
  }
}
