package com.shands.mod.dao.model.training.bo;

import com.shands.mod.dao.model.training.vo.AddCourseLabelVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2022/8/17
 **/
@Data
@ApiModel("添加合集 入参")
public class AddCollectionBo {

    @ApiModelProperty("合集id(仅在修改处用到)")
    private Integer collectionId;

    @ApiModelProperty("合集名称")
    private String name;

    @ApiModelProperty("封面")
    private String imageUrl;

    @ApiModelProperty("合集简介")
    private String courseIntroduction;

    @ApiModelProperty("分组id")
    private String courseGroupId;

    @ApiModelProperty("标签id")
    private String labelId;

    @ApiModelProperty("是否对所有员工有效")
    private Integer ifStaff;

    @ApiModelProperty("品牌codes")
    private String brandCode;

    @ApiModelProperty("事业部codes")
    private String divisionCode;

    @ApiModelProperty("酒店类型（集团或酒店）")
    private String configType;

    @ApiModelProperty("酒店ids")
    private String hotelIds;

    @ApiModelProperty("酒店名")
    private String hotelName;

    @ApiModelProperty("角色codes")
    private String roleCode;

    @ApiModelProperty("角色名")
    private String roleName;

    @ApiModelProperty("状态（已上架，已下架）")
    private Integer status;

    @ApiModelProperty("发布酒店id")
    private Integer releaseHotelId;

    @ApiModelProperty("分组数组")
    private String courseGroupIds;

    @ApiModelProperty("课程标签参数")
    private List<AddCourseLabelVo> courseLabelVos;
}
