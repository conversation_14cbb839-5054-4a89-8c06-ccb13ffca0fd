package com.shands.mod.dao.model.statistics.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: 百达卡指标返回数据
 * @Author: mazhiyong
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("百达卡指标返回数据")
public class BetterwoodCardDataVo {

    @ApiModelProperty("本人数据")
    private BaseStatisticsDataVo personal;

    @ApiModelProperty("本店数据")
    private BaseStatisticsDataVo hotel;
} 