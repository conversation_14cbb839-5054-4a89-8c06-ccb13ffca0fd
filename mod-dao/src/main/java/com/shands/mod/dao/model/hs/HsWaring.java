package com.shands.mod.dao.model.hs;

import java.util.Date;

public class HsWaring {

  /** ID */
  private Integer id;

  /** 服务类型 */
  private String serviceType;

  /** 时长(单位秒) */
  private Integer amount;

  /** 事件 */
  private Integer orderStatus;

  /** 接收部门 */
  private Integer acceptDept;

  /** 接收用户,逗号分隔 */
  private String acceptUser;

  /** 状态 */
  private Integer status;

  /** 所属公司ID */
  private Integer companyId;

  /** 集团ID */
  private Integer groupId;

  /** 版本 */
  private Integer version;

  /** 删除标志 */
  private Byte deleted;

  /** 创建用户 */
  private Integer createUser;

  /** 创建时间 */
  private Date createTime;

  /** 更新用户 */
  private Integer updateUser;

  /** 更新时间 */
  private Date updateTime;

  /** 是否发送短信,0不发送,1发送 */
  private Integer sendSms;

  public Integer getId() {
    return id;
  }

  public void setId(Integer id) {
    this.id = id;
  }

  public String getServiceType() {
    return serviceType;
  }

  public void setServiceType(String serviceType) {
    this.serviceType = serviceType;
  }

  public Integer getAmount() {
    return amount;
  }

  public void setAmount(Integer amount) {
    this.amount = amount;
  }

  public Integer getOrderStatus() {
    return orderStatus;
  }

  public void setOrderStatus(Integer orderStatus) {
    this.orderStatus = orderStatus;
  }

  public Integer getAcceptDept() {
    return acceptDept;
  }

  public void setAcceptDept(Integer acceptDept) {
    this.acceptDept = acceptDept;
  }

  public String getAcceptUser() {
    return acceptUser;
  }

  public void setAcceptUser(String acceptUser) {
    this.acceptUser = acceptUser;
  }

  public Integer getStatus() {
    return status;
  }

  public void setStatus(Integer status) {
    this.status = status;
  }

  public Integer getCompanyId() {
    return companyId;
  }

  public void setCompanyId(Integer companyId) {
    this.companyId = companyId;
  }

  public Integer getGroupId() {
    return groupId;
  }

  public void setGroupId(Integer groupId) {
    this.groupId = groupId;
  }

  public Integer getVersion() {
    return version;
  }

  public void setVersion(Integer version) {
    this.version = version;
  }

  public Byte getDeleted() {
    return deleted;
  }

  public void setDeleted(Byte deleted) {
    this.deleted = deleted;
  }

  public Integer getCreateUser() {
    return createUser;
  }

  public void setCreateUser(Integer createUser) {
    this.createUser = createUser;
  }

  public Date getCreateTime() {
    return createTime;
  }

  public void setCreateTime(Date createTime) {
    this.createTime = createTime;
  }

  public Integer getUpdateUser() {
    return updateUser;
  }

  public void setUpdateUser(Integer updateUser) {
    this.updateUser = updateUser;
  }

  public Date getUpdateTime() {
    return updateTime;
  }

  public void setUpdateTime(Date updateTime) {
    this.updateTime = updateTime;
  }

  public Integer getSendSms() {
    return sendSms;
  }

  public void setSendSms(Integer sendSms) {
    this.sendSms = sendSms;
  }
}
