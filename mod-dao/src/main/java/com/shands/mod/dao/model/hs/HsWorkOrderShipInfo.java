package com.shands.mod.dao.model.hs;

import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 订单用船明细
 */
@Data
@NoArgsConstructor
public class HsWorkOrderShipInfo {
    private Integer id;

    /**
     * 入住和离店标记 1离店2入住
     */
    private Integer inOrOut;

    /**
     * 出发地
     */
    private String start;

    /**
     * 目的地
     */
    private String end;

    /**
     * 单价
     */
    private BigDecimal price;

    /**
     * 总价
     */
    private BigDecimal totalPrice;

    /**
     * 用船人数
     */
    private Integer people;

    /**
     * 时分
     */
    private String time;

    /**
     * 年月日
     */
    private Date date;

    /**
     * 工单id,hs_work_order
     */
    private Integer workOrderId;

    private Integer tool;

    private Integer companyId;
}