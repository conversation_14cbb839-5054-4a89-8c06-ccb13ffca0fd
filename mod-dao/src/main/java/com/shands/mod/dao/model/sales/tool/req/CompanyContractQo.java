package com.shands.mod.dao.model.sales.tool.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @description: 企业企业单店协议酒店合同Qo
 **/
@Data
public class CompanyContractQo {

    @ApiModelProperty(value = "合同id（获取合同详情有就传，无不需要传）")
    private Integer id;

    @ApiModelProperty(value = "酒店编号（后端自动获取，前端不需要传）")
    private String hotelCode;

    @ApiModelProperty(value = "企业id", required = true)
    @NotNull(message = "企业id不能为空")
    private Long companyId;

    @ApiModelProperty(value = "合同开始时间", required = true)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "合同开始时间不能为空")
    private LocalDateTime contractBeginTime;

    @ApiModelProperty(value = "合同结束时间", required = true)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "合同结束时间不能为空")
    private LocalDateTime contractEndTime;

    @ApiModelProperty(value = "合同状态（0:禁用 1:启用）", required = true)
    @NotNull(message = "合同状态不能为空")
    private Integer status;

    @ApiModelProperty(value = "签约销售员uc_id", required = true)
    @NotNull(message = "签约销售员不能为空")
    private Integer salesId;

    @ApiModelProperty(value = "房价码列表", required = true)
    @NotEmpty(message = "房价码列表不能为空")
    private List<String> rateCodeList;

    @ApiModelProperty(value = "附件")
    private List<String> enclosureInfoList;

    @ApiModelProperty(value = "备注")
    private String remark;
}
