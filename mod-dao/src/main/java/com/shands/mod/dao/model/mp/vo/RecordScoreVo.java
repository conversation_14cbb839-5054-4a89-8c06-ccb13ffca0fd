package com.shands.mod.dao.model.mp.vo;

import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Builder
public class RecordScoreVo {

  private Integer id;

  private Double assessScore;

  private Double totalAssessScore;

  private Double synthesizeScore;

  private Date startDate;

  private Date endDate;

  private Integer star;

  private String userName;
}

