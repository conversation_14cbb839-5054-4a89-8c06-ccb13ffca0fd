<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shands.mod.dao.mapper.proprietor.ModProprietorCardSendRecordMapper">

    <resultMap type="com.shands.mod.dao.model.proprietor.ModProprietorCardSendRecord" id="ModProprietorCardSendRecordMap">
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="mobile" column="mobile" jdbcType="VARCHAR"/>
        <result property="areaCode" column="area_code" jdbcType="VARCHAR"/>
        <result property="cardId" column="card_id" jdbcType="BIGINT"/>
        <result property="sendRes" column="send_res" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="delFlag" column="del_flag" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, mobile, area_code, card_id, send_res, create_time, update_time, del_flag
    </sql>


    <!--查询所有发放成功的记录-->
    <select id="selectAllSuccess" resultMap="ModProprietorCardSendRecordMap">
        select
        <include refid="Base_Column_List"/>
        from mod_proprietor_card_send_record
        where send_res = 1 and del_flag = 0
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into mod_proprietor_card_send_record
        (mobile, area_code, card_id, send_res, create_time, update_time)
        values
        (#{mobile}, #{areaCode}, #{cardId}, #{sendRes}, #{createTime}, #{updateTime})
    </insert>

    <!--通过主键修改数据-->
    <update id="deleteByMobile">
        update
          mod_proprietor_card_send_record
        set
            del_flag = 1
        where
          mobile = #{mobile}
    </update>


</mapper>
