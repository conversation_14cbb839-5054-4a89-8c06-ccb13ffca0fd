package com.shands.mod.dao.model.v0701.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/7/28
 * @desc 酒店所有用户返回列表vo
*/

@Data
@ApiModel(value = "酒店所有用户返回列表vo")
public class RemindUserVo implements Serializable {

  @ApiModelProperty(value = "用户编号")
  private Integer userId;

  @ApiModelProperty(value = "部门编号")
  private Integer deptId;

  @ApiModelProperty(value = "用户名称")
  private String userName;

}
