package com.shands.mod.dao.model.app;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/10/10
 **/
@Data
@ApiModel(value = "用户角色酒店信息")
public class CompanyRoles {

  @ApiModelProperty(value = "酒店code")
  private String companyCode;

  @ApiModelProperty(value = "酒店名")
  private String companyName;

  @ApiModelProperty(value = "酒店名")
  private List<RoleView> roleViewList;
}
