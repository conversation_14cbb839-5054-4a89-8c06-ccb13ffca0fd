package com.shands.mod.dao.mapper.hs;

import com.shands.mod.dao.model.hs.GoodsStock;
import com.shands.mod.dao.model.req.hs.goods.*;
import com.shands.mod.dao.model.res.hs.good.*;
import com.shands.mod.dao.model.res.hs.sell.SellRes;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface GoodsStockMapper {

  int deleteByPrimaryKey(Integer id);

  int insert(GoodsStock record);

  int insertSelective(GoodsStock record);

  GoodsStock selectByPrimaryKey(Integer id);

  int updateByPrimaryKeySelective(GoodsStock record);

  int updateByPrimaryKey(GoodsStock record);

  List<GoodStockRes> query(@Param("queryReq") GoodsQueryReq queryReq);

  GoodsEditorOrAdd stockById(@Param("queryReq") GoodsQueryReq queryReq);

  int updateFreezePlus(SellRes sellRes);

  // 30分钟
  int updateFreezeSubtractMin(SellRes sellRes);

  // 工单完成解冻
  int updateFreezeSubtract(SellRes sellRes);


  Integer updateShelfByTypeId(Map map);

  List<GoodsStockListRes> getStoctByTypeId(@Param("queryReq") ListReq listReq);

  SpeedyRes getSpeedy(@Param("queryReq") SpeedyReq listReq);

  List<SpeedyRes> getCount(@Param("queryReq") ListReq listReq);

  List<GoodsListByServiceId> stockList(UpStockByOrderReq upStockByOrderReq);

  List<GoodsStockListRes> rentAll(@Param("queryReq") ListReq listReq);

  GoodsStockListRes detail(ListReq listReq);

  GoodsListByServiceId forOrder(
      @Param("hotelServiceId") Integer hotelServiceId, @Param("goodsId") Integer goodsId);
}
