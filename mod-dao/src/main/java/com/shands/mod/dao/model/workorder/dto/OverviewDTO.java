package com.shands.mod.dao.model.workorder.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Builder
public class OverviewDTO {

  private int total;

  private int completeNum;

  private Double completeRate;

  private int solveNum;

  private Double solveRate;

  private int outTimeNum;

  private Double outTimeRate;

  private long totalTime;

  private long avgTime;
}
