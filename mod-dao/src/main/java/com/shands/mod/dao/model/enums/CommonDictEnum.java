package com.shands.mod.dao.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 字典枚举类
 */
@Getter
@AllArgsConstructor
public enum CommonDictEnum {
  MANAGER_DATA_MEM("MANAGER_DATA_MEM", "面对面会员发展完成率"),
  MANAGER_DATA_MEM_COMPANY("MANAGER_DATA_MEM_COMPANY", "企业会员发展完成率"),
  MANAGER_DATA_ROOM("MANAGER_DATA_ROOM", "官渠间夜占比"),
  MANAGER_DATA_COMMENT("MANAGER_DATA_COMMENT", "新增点评均分"),
  MANAGER_DATA_COMMENT_DAY_ROOM("MANAGER_DATA_COMMENT_DAY_ROOM", "新增单房网评量占比"),
  MANAGER_DATA_INCOME("MANAGER_DATA_INCOME", "营业收入"),
  MANAGER_DATA_UNIFORM("MANAGER_DATA_UNIFORM", "统采率"),
  MANAGER_DATA_PREPARATION("MANAGER_DATA_PREPARATION", "筹备完成情况"),
  EXIT("PSTATUS&EXIT","退出"),
  OPEN("PSTATUS&OPEN","在营"),
  WAIT("PSTATUS&WAIT","筹开"),
  PREPARE("PSTATUS&PREPARE","筹建"),
  SIGN("PSTATUS&SIGN","签约"),
  STOP("PSTATUS&STOP","中止");
  ;

  //字典编码
  private String dictCode;

  //字典表述
  private String dictName;

  public static String getValue(String key){
    CommonDictEnum[] values = CommonDictEnum.values();
    for (CommonDictEnum value : values){
      if (value.dictCode.equals(key)){
        return value.name();
      }
    }
    return "";
  }
}
