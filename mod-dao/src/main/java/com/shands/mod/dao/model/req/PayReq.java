package com.shands.mod.dao.model.req;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/** 支付请求参数 */
@Data
@NoArgsConstructor
public class PayReq {
  /** 支付订单 */
  @NotNull(message = "支付订单号不能为空")
  private String payOrderNO;

  /** 支付方式:支付宝 ZFB\微信WX */
  @NotNull(message = "支付方式不能为空")
  private String platform;

  @NotNull(message = "支付来源不能为空")
  private String source;

  /** 支付类型 ZFB:client , APP WX: H5,APP,client,NATIVE(二维码) */
  @NotNull(message = "支付类型不能为空")
  private String payType;
}
