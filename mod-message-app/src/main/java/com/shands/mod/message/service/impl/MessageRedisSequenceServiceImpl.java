package com.shands.mod.message.service.impl;

import com.shands.mod.message.service.IMessageSequenceService;
import com.shands.mod.util.BaseConstants;
import com.shands.mod.util.Tools;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.Collections;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-08-02
 * @description redis序列
 */
@Slf4j
@Service
public class MessageRedisSequenceServiceImpl implements IMessageSequenceService {

  private static final int NUMBER_LENGTH = 4;

  @Resource
  private RedisTemplate redisTemplate;

  @Override
  public String generateSequence(Integer length) {
// 当前时间戳
    String yyyyMMdd =
        DateFormatUtils.format(System.currentTimeMillis(), BaseConstants.FORMAT_SHORT_DATE);

    String seqDate = null;
    String keyDate = Tools.buildKey(BaseConstants.CACHE_PREFIX, "MESSAGE_SEQUENCE_DATE");
    String keyValue = Tools.buildKey(BaseConstants.CACHE_PREFIX, "MESSAGE_SEQUENCE_VALUE");
    Object obj = this.redisTemplate.opsForValue().get(keyDate);
    if (obj != null) {
      seqDate = obj.toString();
    }

    Long val = 0L;
    if (yyyyMMdd.equals(seqDate)) {
      val = this.redisTemplate.opsForValue().increment(keyValue, 1L);
    } else {
      val = 1L;
      // 由于使用了Fastjson进行序列化，redis中的值都是带引号的JSON字符串
      // this.redisTemplate.opsForValue().set(PepConstants.CACHE_CLUE_SEQUENCE_VALUE,
      // String.valueOf(val));
      String script = "return redis.call('set', '" + keyValue + "', 1)";
      DefaultRedisScript<String> rs = new DefaultRedisScript<>();
      rs.setScriptText(script);
      rs.setResultType(String.class);
      StringRedisSerializer redisSerializer = new StringRedisSerializer();
      this.redisTemplate.execute(
          rs, redisSerializer, redisSerializer, Collections.singletonList(null));

      this.redisTemplate.opsForValue().set(keyDate, yyyyMMdd);
    }
    // 函数获取今天自增数量
    String result =
        yyyyMMdd
            + StringUtils.leftPad(
            String.valueOf(val), length != null ? length : NUMBER_LENGTH, "0");

    return result;
  }
}
