package com.shands.mod.main.remote.roomPrice.fallback;


import com.betterwood.base.common.model.PageVO;
import com.betterwood.base.common.model.Result;
import com.betterwood.base.common.util.ResultMessageUtil;
import com.shands.mod.main.remote.roomPrice.RoomPriceRemoteService;
import com.shands.mod.main.service.betterwood.rpc.resp.PricePlanHotelVO;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

@Component
public class RoomPriceRemoteServiceFallBack implements FallbackFactory<RoomPriceRemoteService> {

    @Override
    public RoomPriceRemoteService create(Throwable cause) {
        return new RoomPriceRemoteService() {
            @Override
            public Result<PageVO<PricePlanHotelVO>> page(Integer pageNo, Integer pageSize, String codeName, String templateSrc, String isHalt, String hotelCode, String newData, String isNormal, String salesObj, String useBasePrice, String onlineChannel, String deptCode) {
                return ResultMessageUtil.failResponse("查询房价码列表失败");
            }
        };
    }
}
