package com.shands.mod.main.service.betterwood;

import com.github.pagehelper.PageInfo;
import com.shands.mod.dao.model.sales.tool.domain.ModCompanyApplyRecord;
import com.shands.mod.dao.model.sales.tool.req.CompanyApplyReq;
import com.shands.mod.dao.model.sales.tool.req.CompanyMemberPageListReq;
import com.shands.mod.dao.model.sales.tool.req.CompanyPageListReq;
import com.shands.mod.dao.model.sales.tool.res.*;
import com.shands.mod.main.service.betterwood.rpc.resp.CompanyVO;
import org.springframework.web.multipart.MultipartFile;
import java.util.List;

public interface CompanyMemberService {

  PageInfo<CompanyPageListRes> queryCompanyPageList(CompanyPageListReq req);

  PageInfo<CompanyMemberPageListRes> queryMemberPageList(CompanyMemberPageListReq req);

  UploadBizLicenseRes bizLicenseUpload(MultipartFile file, Integer uploadType);

  PublicEmailRes getPublicEmailSuffix();

  CompanyVO companyApply(CompanyApplyReq req);

  PageInfo<ModCompanyApplyRecord> getCompanyApplyRecordPage(Integer pageNo, Integer pageSize);

  CompanyContractRes queryContractByCompanyId(Long companyId);

  /**
   * 获取房价码列表
   * @return 房价码列表
   */
  List<RateCodeRes> queryRateCodeList();

  /**
   * 合同附件上传
   * @param file 上传的文件
   * @return 文件URL
   */
  String contractAttachmentUpload(MultipartFile file);

  /**
   * 企业合同提交确认
   * @param req 合同信息
   * @return 是否成功
   */
  Boolean companyContractAddOrUpdate(com.shands.mod.dao.model.sales.tool.req.CompanyContractAddOrUpdateReq req);
}
