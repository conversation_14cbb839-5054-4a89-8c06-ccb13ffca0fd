package com.shands.mod.main.service.betterwood.rpc.resp;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @description: 营销价格房价码详情基础VO类
 * @author: lihui
 * @create: 2023-12-05 11:06
 **/
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@ApiModel(value = "房价码详情对象")
@Data
public class PromotionRateCodeDetailBaseVO implements Serializable {
    @ApiModelProperty(value = "集团代码")
    private String hotelGroupCode;
    @ApiModelProperty(value = "酒店代码")
    private String hotelCode;
    @ApiModelProperty(value = "房价码代码")
    private String rateCode;
    @ApiModelProperty(value = "新产品代码=hotelCode+rateCode+rmtype")
    private String productCode;
    @ApiModelProperty(value = "房价码名称")
    private String rateCodeName;
    @ApiModelProperty(value = "房价码代码描述")
    private String rateCodeDesc;
    @ApiModelProperty(value = "是否可订(T/F)")
    private String bookAble;
    @ApiModelProperty(value = "房型代码")
    private String roomTypeCode;
    @ApiModelProperty(value = "房型代码描述")
    private String roomTypeCodeDesc;
    @ApiModelProperty(value = "币种")
    private String currency;
    @ApiModelProperty(value = "是否钟点房房价码/房价类型T,F")
    private String isHourRatecode;
    @ApiModelProperty(value = "最小预定时长")
    private Integer hours;
    @ApiModelProperty(value = "最大预定时长")
    private Integer maxHours;
    @ApiModelProperty(value = "可订时间段-开始")
    private String arrivalFrom;
    @ApiModelProperty(value = "可订时间段-结束")
    private String arrivalTo;
    @ApiModelProperty(value = "起步价")
    private BigDecimal startingPrice;
    @ApiModelProperty(value = "超时价格")
    private BigDecimal overtimePrice;
    @ApiModelProperty(value = "房价码预定规则详情 -后续废弃")
    private PromotionResrvRuleInfoVO resrvRuleInfoDTO;
    @ApiModelProperty(value = "房价码预定规则详情列表 -采用多个预定规则")
    private List<PromotionResrvRuleInfoVO> resrvRuleInfoDTOList;
    @ApiModelProperty(value = "房价码额外详情")
    private PromotionRateCodeExtendInfoVO extendInfo;
    @ApiModelProperty(value = "房型大类")
    private String roomMainType;
    @ApiModelProperty(value = "市场码")
    private String market;
    @ApiModelProperty(value = "市场码名称")
    private String marketName;
    @ApiModelProperty(value = "每日明细，包含房量、房态、房价")
    private List<PromotionRateEveryDayDetailVO> everyDetails;
    @ApiModelProperty(value = "二级渠道编码")
    private String secondChannelCode;
    @ApiModelProperty(value = "身份编码")
    private String identityCode;
    @ApiModelProperty(value = "身份名称")
    private String identityName;
    @ApiModelProperty("是否走营销中心")
    private Boolean isSell = Boolean.FALSE;
    @ApiModelProperty(value = "企业id")
    private String companyId;

}
