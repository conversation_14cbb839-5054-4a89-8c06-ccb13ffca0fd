package com.shands.mod.main.controller.sales.tool;

import com.betterwood.log.core.annotation.ResultLog;
import com.betterwood.log.core.enums.MethodTypeEnum;
import com.github.pagehelper.PageInfo;
import com.shands.mod.dao.model.sales.tool.domain.ModCompanyApplyRecord;
import com.shands.mod.dao.model.sales.tool.req.CompanyApplyReq;
import com.shands.mod.dao.model.sales.tool.req.CompanyContractAddOrUpdateReq;
import com.shands.mod.dao.model.sales.tool.req.CompanyMemberPageListReq;
import com.shands.mod.dao.model.sales.tool.req.CompanyPageListReq;
import com.shands.mod.dao.model.sales.tool.res.*;
import com.shands.mod.main.service.betterwood.CompanyMemberService;
import com.shands.mod.main.service.betterwood.rpc.resp.CompanyVO;
import com.shands.mod.vo.ResultVO;
import java.util.List;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import javax.validation.Valid;

@RestController
@RequestMapping(value = "/company")
@Api(value = "企订通-企业会员",tags = "企订通-企业会员")
@Slf4j
public class CompanyMemberController {


  @Autowired
  private CompanyMemberService companyMemberService;

  @GetMapping("/pageList")
  @ApiOperation(value = "企订通-企业分页列表查询")
  @ResultLog(name = "CompanyMemberController.queryPageList", methodType = MethodTypeEnum.HTTP_UP)
  public ResultVO<PageInfo<CompanyPageListRes>> queryPageList(@Valid CompanyPageListReq req) {
    return ResultVO.success(companyMemberService.queryCompanyPageList(req));
  }


  @GetMapping("/member/pageList")
  @ApiOperation(value = "企订通-企业会员分页列表查询")
  @ResultLog(name = "CompanyMemberController.queryMemberPageList", methodType = MethodTypeEnum.HTTP_UP)
  public ResultVO<PageInfo<CompanyMemberPageListRes>> queryMemberPageList(@Valid CompanyMemberPageListReq req) {
    return ResultVO.success(companyMemberService.queryMemberPageList(req));
  }
  @PostMapping("/bizLicense/upload")
  @ApiOperation(value = "营业执照上传+OCR识别")
  @ResultLog(name = "CompanyMemberController.bizLicenseUpload", methodType = MethodTypeEnum.HTTP_UP)
  public ResultVO<UploadBizLicenseRes> bizLicenseUpload(@RequestParam("file") MultipartFile file,
      @RequestParam(value = "uploadType", defaultValue = "0") Integer uploadType) {
    return ResultVO.success(companyMemberService.bizLicenseUpload(file, uploadType));
  }

  @GetMapping("/email/public")
  @ApiOperation(value = "公共邮箱后缀获取")
  @ResultLog(name = "CompanyMemberController.getPublicEmailSuffix", methodType = MethodTypeEnum.HTTP_UP)
  public ResultVO<PublicEmailRes> getPublicEmailSuffix() {
    return ResultVO.success(companyMemberService.getPublicEmailSuffix());
  }


  @PostMapping("/apply")
  @ApiOperation(value = "企业申请")
  @ResultLog(name = "CompanyMemberController.companyApply", methodType = MethodTypeEnum.HTTP_UP)
  public ResultVO<CompanyVO> companyApply(@Valid @RequestBody CompanyApplyReq req) {
    return ResultVO.success(companyMemberService.companyApply(req));
  }


  @GetMapping("/apply/record")
  @ApiOperation(value = "分页获取企业申请记录")
  @ResultLog(name = "CompanyMemberController.getCompanyApplyRecordPage", methodType = MethodTypeEnum.HTTP_UP)
  public ResultVO<PageInfo<ModCompanyApplyRecord>> getCompanyApplyRecordPage(@RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
                                                                            @RequestParam(value = "pageSize", defaultValue = "15") Integer pageSize) {
    return ResultVO.success(companyMemberService.getCompanyApplyRecordPage(pageNo, pageSize));
  }

  @GetMapping("/contract/queryByCompanyId")
  @ApiOperation(value = "企订通-合同详情查询")
  @ResultLog(name = "CompanyMemberController.queryPageList", methodType = MethodTypeEnum.HTTP_UP)
  public ResultVO<CompanyContractRes> queryContractByCompanyId(@RequestParam("companyId") Long companyId) {
    return ResultVO.success(companyMemberService.queryContractByCompanyId(companyId));
  }

  @GetMapping("/contract/queryRateCode")
  @ApiOperation(value = "获取房价码列表")
  @ResultLog(name = "CompanyMemberController.queryRateCode", methodType = MethodTypeEnum.HTTP_UP)
  public ResultVO<List<RateCodeRes>> queryRateCode() {
    return ResultVO.success(companyMemberService.queryRateCodeList());
  }

  @PostMapping("/contract/upload")
  @ApiOperation(value = "合同附件上传")
  @ResultLog(name = "CompanyMemberController.contractAttachmentUpload", methodType = MethodTypeEnum.HTTP_UP)
  public ResultVO<String> contractAttachmentUpload(@RequestParam("file") MultipartFile file) {
    return ResultVO.success(companyMemberService.contractAttachmentUpload(file));
  }
}
