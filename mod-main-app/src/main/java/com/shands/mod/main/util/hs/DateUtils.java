package com.shands.mod.main.util.hs;

import com.shands.mod.exception.NormalException;
import java.sql.Time;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.Data;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class DateUtils {

  public static final String DATE_FORMAT_Y_M_DHM = "HH:mm";
  public static final String DATE_FORMAT_YYYY_MM_DD = "yyyy-MM-dd";
  public static final String DATE_FORMAT_YYYY_MM_DD_HMS = "yyyy-MM-dd HH:mm:ss";
  public static final String DATE_FORMAT_YYYY_MM_DD_HMS_BEGIN = "yyyy-MM-dd 00:00:00";
  public static final String DATE_FORMAT_YYYY_MM_DD_HMS_END = "yyyy-MM-dd 59:59:59";
  public static final String DATE_FORMAT_YYYY_MM = "yyyy-MM";
  public static final String DATE_FORMAT_MM_DD_HM = "MM月dd日 HH:mm";
  public static final String DATE_FORMAT_MM_DD = "MM/dd";
  public static final String DATE_FORMAT_MM_DD_HM_2 = "MM/dd HH:mm";

  private DateUtils() {
  }

  /**
   * localDate to date
   */
  public static Date localDateToDate(LocalDate localDate) {
    return Date.from(localDate.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
  }

  public static Date stringToDate(String str, String format) throws ParseException {
    DateFormat definedDate = new SimpleDateFormat(format);
    return definedDate.parse(str);
  }

  public static LocalDate dateToLocalDate(Date date) {
    Instant instant = date.toInstant();
    ZoneId zone = ZoneId.systemDefault();
    LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, zone);
    LocalDate localDate = localDateTime.toLocalDate();
    return localDate;
  }

  public static LocalDateTime dateToLocalDateTime(Date date) {
    Instant instant = date.toInstant();
    ZoneId zoneId = ZoneId.systemDefault();
    LocalDateTime localDateTime = instant.atZone(zoneId).toLocalDateTime();
    return localDateTime;
  }

  /**
   * localDateTime to date
   */
  public static Date localDateTimeToDate(LocalDateTime localDateTime) {
    return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
  }

  /**
   * localTime to time
   *
   * @param localTime
   * @return
   */
  public static Time localTimeToTime(LocalTime localTime) {
    return Time.valueOf(localTime);
  }

  public static Boolean time(LocalDateTime start, LocalDateTime end) {
    LocalDate localDate = start.toLocalDate();
    LocalDate localDate1 = end.toLocalDate();
    long day = localDate1.toEpochDay() - localDate.toEpochDay();
    if (day > 0) {
      return true;
    } else {
      return false;
    }
  }

  public static String dateToString(Date date, String str) {

    SimpleDateFormat formatter = new SimpleDateFormat(str);
    return formatter.format(date);
  }

  /**
   * 当前时间延后n秒
   *
   * @param n
   * @return
   */
  public static Date nowAdd(Integer n) {
    Date date = new Date();
    long time = date.getTime();
    long l = time + Long.valueOf(n);
    return new Date(l);
  }

  /**
   * 输入秒 返回时分秒
   *
   * @param sec
   * @return
   */
  public static String secToString(Integer sec) {

    if (sec <= 0) {
      return "0秒";
    }

    int h = sec / 3600;
    int m = sec % 3600 / 60;
    int s = sec % 60; // 不足60的就是秒，够60就是分
    if (h > 0) {

      return h + "小时" + m + "分钟" + s + "秒";
    } else if (m > 0) {
      if (s > 0) {
        return m + "分钟" + s + "秒";
      } else {
        return m + "分钟";
      }
    } else if (s > 0) {
      return s + "秒";
    } else {
      return h + "小时" + m + "分钟" + s + "秒";
    }
  }

  /**
   * 计算两个 date 的时间差
   */
  public static Long timeDiff(Date start, Date end) throws NormalException {
    if (start == null || end == null) {
      throw new NormalException("输入参数异常");
    }
    return end.getTime() - start.getTime();
  }

  public static String dateToStr(Date date) {
    String strDate = "";
    SimpleDateFormat sdf = new SimpleDateFormat(DATE_FORMAT_Y_M_DHM);
    strDate = sdf.format(date);
    return strDate;
  }

  public static Date strToDate(String date) throws ParseException {
    Date strDate = null;
    SimpleDateFormat sdf = new SimpleDateFormat(DATE_FORMAT_Y_M_DHM);
    strDate = sdf.parse(date);
    return strDate;
  }

  /**
   * 查看 多个时间段有没有重叠
   *
   * @param list
   * @return
   */
  @SneakyThrows
  public static String checkSelf(List<TimeModel> list) {
    String res = null;
    if (list.size() == 0) {
      return res;
    }
    for (int i = 0; i < list.size(); i++) {
      Date I_S = strToDate(list.get(i).getStartTime());
      Date I_E = strToDate(list.get(i).getEndTime());
      for (int j = i + 1; j < list.size(); j++) {
        Date J_S = strToDate(list.get(j).getStartTime());
        Date J_E = strToDate(list.get(j).getEndTime());
        //这里使用compareTo方法, 因为getTime()的时间不太准确
        if ((J_S.compareTo(I_S) == -1 && I_S.compareTo(J_E) == -1) || (J_S.compareTo(I_E) == -1
            && I_E.compareTo(J_E) == -1) || (I_S.compareTo(J_S) == -1 && J_S.compareTo(I_E) == -1)
            || (I_S.compareTo(J_E) == -1 && J_E.compareTo(I_E) == -1)   //新加部分
            || J_E.compareTo(I_S) == 0 || J_S.compareTo(I_E) == 0
            || J_E.compareTo(I_E) == 0 || J_S.compareTo(I_S) == 0) {
          res = dateToStr(strToDate(list.get(i).getStartTime())) + " "
              + dateToStr(strToDate(list.get(i).getEndTime()));
          break;
        }
      }
    }
    return res;
  }

  /**
   * 在当前日期上增加天数
   *
   * @param days 天数
   * @return {@link String}
   */
  public static String addDays(Integer days) {
    SimpleDateFormat sf = new SimpleDateFormat(DATE_FORMAT_YYYY_MM_DD);
    Calendar c = Calendar.getInstance();
    c.add(Calendar.DAY_OF_MONTH, days);
    return sf.format(c.getTime());
  }

  /**
   * str转换日期
   *
   * @param date 日期
   * @return {@link Date}
   * @throws ParseException 解析异常
   */
  public static Date strConverDate(String date) throws ParseException {
    Date strDate;
    SimpleDateFormat sdf = new SimpleDateFormat(DATE_FORMAT_YYYY_MM_DD);
    strDate = sdf.parse(date);
    return strDate;
  }

  /**
   * str转换日期
   *
   * @param date 日期
   * @return {@link Date}
   * @throws ParseException 解析异常
   */
  public static Date strConverDate(String date,String format) throws ParseException {
    Date strDate;
    SimpleDateFormat sdf = new SimpleDateFormat(format);
    strDate = sdf.parse(date);
    return strDate;
  }

  /**
   * str转换日期
   *
   * @param date 日期
   * @return {@link Date}
   * @throws ParseException 解析异常
   */
  public static Date strConverDateyyss(String date) throws ParseException {
    Date strDate;
    SimpleDateFormat sdf = new SimpleDateFormat(DATE_FORMAT_YYYY_MM_DD_HMS);
    strDate = sdf.parse(date);
    return strDate;
  }

  /**
   * 是否是下午
   *
   * @return boolean
   */
  public static boolean isAfternoon() {
    int now = Calendar.getInstance().getTime().getHours();
    return (now - 12) > 0;
  }

  /**
   * 判断日期是不是今天
   *
   * @param date 日期
   * @return 是返回true，不是返回false
   */
  public static boolean isNow(Date date) {
    // 当前时间
    Date now = new Date();
    SimpleDateFormat sf = new SimpleDateFormat(DATE_FORMAT_YYYY_MM_DD);
    //获取今天的日期
    String nowDay = sf.format(now);
    //对比的时间
    String day = sf.format(date);

    return day.equals(nowDay);
  }

  /**
   * 判断日期是否是当月
   *
   * @param date
   * @return
   */
  public static boolean isMouth(String date) {
    // 当前时间
    Date now = new Date();
    SimpleDateFormat sf = new SimpleDateFormat(DATE_FORMAT_YYYY_MM);
    //获取今天的日期
    String nowDay = sf.format(now);

    return date.equals(nowDay);
  }

  /**
   * 日期是否早于今天
   *
   * @param date 日期
   * @return boolean
   */
  public static boolean lesstoday(Date date) {
    SimpleDateFormat sf = new SimpleDateFormat(DATE_FORMAT_YYYY_MM_DD);
    //获取今天的日期
    String previousDay = sf.format(new Date());
    try {
      Long diff = timeDiff(date, strConverDate(previousDay));
      return diff > 0;
    } catch (Exception e) {
      log.error(e.getMessage(), e);
    }
    return false;
  }

  /**
   * 日期是否早于昨天
   *
   * @param date 日期
   * @return boolean
   */
  public static boolean lessYestoday(Date date) {
    SimpleDateFormat sf = new SimpleDateFormat(DATE_FORMAT_YYYY_MM_DD);
    //获取今天的日期
    String previousDay = sf.format(getPreviousDay(new Date()));
    try {
      Long diff = timeDiff(date, strConverDate(previousDay));
      return diff > 0;
    } catch (Exception e) {
      log.error(e.getMessage(), e);
    }
    return false;
  }

  /**
   * 获取前一天日期
   *
   * @param date 日期
   * @return {@link Date}
   */
  public static Date getPreviousDay(Date date) {
    Calendar calendar = Calendar.getInstance();
    calendar.setTime(date);
    calendar.add(Calendar.DAY_OF_MONTH, -1);
    date = calendar.getTime();
    return date;
  }

  /**
   * 相差时间转换
   */
  public static String timeToString(Date startTime, Date endTime) {
    try {
      //返回秒
      Long time = timeDiff(startTime, endTime) / 1000;
      return time.toString();
    } catch (NormalException e) {
      log.error(e.getMessage(), e);
    }
    return "0";
  }

  /**
   * 根据年 月 获取对应的月份 天数
   */
  public static int getDaysByYearMonth(int year, int month) {
    Calendar a = Calendar.getInstance();
    a.set(Calendar.YEAR, year);
    a.set(Calendar.MONTH, month - 1);
    a.set(Calendar.DATE, 1);
    a.roll(Calendar.DATE, -1);
    int maxDate = a.get(Calendar.DATE);
    return maxDate;
  }

  /**
   * 判断当前时间是否在设置的date1 date2时间段内
   *
   * @param date1: 开始时间(hh:mm)
   * @param date2: 结束时间(hh:mm)
   * @return boolean
   */
  public static boolean isBelongPeriodTime(String date1, String date2) {
    SimpleDateFormat df = new SimpleDateFormat("HH:mm");

    Date currentTime = new Date(System.currentTimeMillis());

    Date startTimeDate;

    Date endTimeDate;

    Calendar date = Calendar.getInstance();

    Calendar begin = Calendar.getInstance();

    Calendar end = Calendar.getInstance();

    try {
      date.setTime(df.parse(df.format(currentTime)));

      startTimeDate = df.parse(date1);

      endTimeDate = df.parse(date2);

      begin.setTime(startTimeDate);

      end.setTime(endTimeDate);

      if (endTimeDate.getHours() < startTimeDate.getHours()) {

        return date.after(begin) || date.before(end);

      } else if (endTimeDate.getHours() == startTimeDate.getHours()) {
        if (endTimeDate.getMinutes() < startTimeDate.getMinutes()) {

          return date.after(begin) || date.before(end);

        }
      }

    } catch (ParseException e) {
      log.error(e.getMessage(), e);

    }

    //这里是时间段的起止都在同一天的情况，只需要判断当前时间是否在这个时间段内即可
    return date.after(begin) && date.before(end);

  }

  /**
   * 获取当前周开始、结束日期
   *
   * @return {@link Map}<{@link String}, {@link Date}>
   */
  public static Map<String, Date> getWeekDate() {
    Map<String, Date> map = new HashMap<>();

    Calendar cal = Calendar.getInstance();
    // 设置一个星期的第一天 (目前设置一周第一天为星期一)
    cal.setFirstDayOfWeek(Calendar.MONDAY);
    // 获得当前日期是一个星期的第几天
    int dayWeek = cal.get(Calendar.DAY_OF_WEEK);
    if (dayWeek == 1) {
      dayWeek = 8;
    }

    // 根据日历的规则，给当前日期减去星期几与一个星期第一天的差值
    cal.add(Calendar.DATE, cal.getFirstDayOfWeek() - dayWeek);
    Date mondayDate = cal.getTime();

    cal.add(Calendar.DATE, 4 + cal.getFirstDayOfWeek());
    Date sundayDate = cal.getTime();

    map.put("startTime", mondayDate);
    map.put("endTime", sundayDate);

    return map;
  }

  /**
   * 获取当前周开始、结束日期
   *
   * @return {@link Map}<{@link String}, {@link Date}>
   */
  public static Map<String, Date> getWeekDate(Date date) {
    Map<String, Date> map = new HashMap<>();
    // 将 Date 对象转换为 LocalDate
    LocalDate localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

    // 计算开始日期（周一）和结束日期（周日）
    LocalDate mondayDate = localDate.with(DayOfWeek.MONDAY);
    LocalDate sundayDate = localDate.with(DayOfWeek.SUNDAY);

    // 将 LocalDate 对象转换回 Date 对象
    Date startDate = Date.from(mondayDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
    Date endDate = Date.from(sundayDate.atStartOfDay(ZoneId.systemDefault()).toInstant());

    map.put("startTime", startDate);
    map.put("endTime", endDate);
    return map;
  }

  /**
   * 获取当前时间在该月的第几天
   *
   * @return {@link Integer}
   */
  public static Integer getDayOfMonth() {

    Date date = new Date();
    Calendar ca = Calendar.getInstance();
    ca.setTime(date);

    return ca.get(Calendar.DAY_OF_MONTH);
  }

  public static Integer getDayOfMonth(Date date) {
    Calendar ca = Calendar.getInstance();
    ca.setTime(date);

    return ca.get(Calendar.DAY_OF_MONTH);
  }

  /**
   * 获取当前月份
   *
   * @return {@link Integer}
   */
  public static Integer getCurrentMonth() {
    Calendar calendar = Calendar.getInstance();
    return calendar.get(Calendar.MONTH) + 1;
  }

  /**
   * 获取当月的最后一天
   *
   * @return {@link Date}
   */
  public static Date getLastDayOfCurrentMonth() {
    Calendar cale = Calendar.getInstance();
    cale.add(Calendar.MONTH, 1);
    cale.set(Calendar.DAY_OF_MONTH, 0);
    return cale.getTime();
  }

  public static Date getLastDayOfCurrentMonth(Date date) {
    Calendar cale = Calendar.getInstance();
    cale.setTime(date);
    cale.add(Calendar.MONTH, 1);
    cale.set(Calendar.DAY_OF_MONTH, 0);
    return cale.getTime();
  }

  /**
   * 获取当月的第一天
   *
   * @return {@link Date}
   */
  public static Date getFirstDayOfCurrentMonth() {
    Calendar cale = Calendar.getInstance();
    cale.add(Calendar.MONTH, 0);
    cale.set(Calendar.DAY_OF_MONTH, 1);
    return cale.getTime();
  }

  /**
   * 获取某年最后一天日期
   *
   * @param year 年份
   * @return Date
   */
  public static Date getYearLast(int year) {
    Calendar calendar = Calendar.getInstance();
    calendar.clear();
    calendar.set(Calendar.YEAR, year);
    calendar.roll(Calendar.DAY_OF_YEAR, -1);
    return calendar.getTime();
  }

  /**
   * 获取某年第一天日期
   *
   * @param year 年份
   * @return Date
   */
  public static Date getYearFirst(int year) {
    Calendar calendar = Calendar.getInstance();
    calendar.clear();
    calendar.set(Calendar.YEAR, year);
    Date currYearFirst = calendar.getTime();
    return currYearFirst;
  }

  public static Date getLastDayOfMonth(Integer month) {
    Calendar cale = Calendar.getInstance();
    cale.set(Calendar.MONTH, month);
    cale.set(Calendar.DAY_OF_MONTH, 0);
    return cale.getTime();
  }

  public static Date getLastDayOfMonth(Integer month, Date date) {
    Calendar cale = Calendar.getInstance();
    cale.setTime(date);
    cale.set(Calendar.MONTH, month);
    cale.set(Calendar.DAY_OF_MONTH, 0);
    return cale.getTime();
  }

  /**
   * 设置当前月日期
   *
   * @return {@link Date}
   */
  public static Date setDayOfMonth(Integer dayOfMonth) {
    Calendar cale = Calendar.getInstance();
    cale.add(Calendar.MONTH, 0);
    cale.set(Calendar.DAY_OF_MONTH, dayOfMonth);
    return cale.getTime();
  }

  public static Date setDayOfMonth(Integer dayOfMonth, Date date) {
    Calendar cale = Calendar.getInstance();
    cale.setTime(date);
    cale.add(Calendar.MONTH, 0);
    cale.set(Calendar.DAY_OF_MONTH, dayOfMonth);
    return cale.getTime();
  }

  /**
   * 设定当年 日期
   *
   * @param month 月份
   * @param day   日期
   * @return {@link Date}
   */
  public static Date setDayOfYear(Integer month, Integer day) {
    Calendar cale = Calendar.getInstance();
    cale.set(Calendar.MONTH, month - 1);
    cale.set(Calendar.DAY_OF_MONTH, day);
    return cale.getTime();
  }

  /**
   * 设定当年 日期
   *
   * @param month 月份
   * @param day   日期
   * @return {@link Date}
   */
  public static Date setDayOfYear(Integer month, Integer day, Date date) {
    Calendar cale = Calendar.getInstance();
    cale.setTime(date);
    cale.set(Calendar.MONTH, month - 1);
    cale.set(Calendar.DAY_OF_MONTH, day);
    return cale.getTime();
  }


  public static Date getFirstDayOfMonth(String month) throws ParseException {
    int year = Integer.parseInt(month.substring(0, 4));  //截取出年份，并将其转化为int
    int month1 = Integer.parseInt(month.substring(5, 7));    //截去除月份，并将其转为int

    Calendar cal = Calendar.getInstance();
    cal.set(Calendar.YEAR, year);    //设置年份
    cal.set(Calendar.MONTH, month1 - 1);  //设置月份
    int firstDay = cal.getActualMinimum(Calendar.DAY_OF_MONTH); //获取某月最小天数
    cal.set(Calendar.DAY_OF_MONTH, firstDay);   //设置日历中月份的最小天数
    //格式化日期
    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
    String firstDayOfMonth = sdf.format(cal.getTime());
    return sdf.parse(firstDayOfMonth);

  }

  //获取某年某月最后一天
  public static Date getLastDayOfMonth(String month) throws ParseException {
    int year = Integer.parseInt(month.substring(0, 4));  //截取出年份，并将其转化为int
    int month1 = Integer.parseInt(month.substring(5, 7));    //截去除月份，并将其转为int

    Calendar cal = Calendar.getInstance();
    cal.set(Calendar.YEAR, year);    //设置年份
    cal.set(Calendar.MONTH, month1 - 1);  //设置月份
    int lastDay = cal.getActualMaximum(Calendar.DAY_OF_MONTH);  //获取某月最大天数
    cal.set(Calendar.DAY_OF_MONTH, lastDay);    //设置日历中月份的最大天数
    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
    String firstDayOfMonth = sdf.format(cal.getTime());
    return sdf.parse(firstDayOfMonth);
  }

  @Data
  public static class TimeModel {

    private String startTime;//开始时间
    private String endTime;   //结束时间
  }
}
