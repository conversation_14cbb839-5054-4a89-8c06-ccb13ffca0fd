package com.shands.mod.main.service.feishu.impl;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Lists;
import com.shands.mod.dao.mapper.datarevision.DataBoardIndexMapper;
import com.shands.mod.dao.mapper.datarevision.DwsMapDistributionMapper;
import com.shands.mod.dao.model.datarevision.bo.HotelProjectMapBo;
import com.shands.mod.dao.model.datarevision.vo.GeneralDrawingVo;
import com.shands.mod.dao.model.datarevision.vo.HotelProjectMapVo;
import com.shands.mod.dao.model.enums.board.DataTypeEnum;
import com.shands.mod.dao.model.feishu.DayValueItem;
import com.shands.mod.dao.model.feishu.FeishuChannelRateItemVo;
import com.shands.mod.dao.model.feishu.FeishuInvestRentRoomPriceVo;
import com.shands.mod.dao.model.feishu.FeishuMembershipContributeItemVo;
import com.shands.mod.dao.model.feishu.FeishuSummaryDataVo;
import com.shands.mod.dao.model.newDataBoard.po.RoomNightItem;
import com.shands.mod.dao.model.newDataBoard.vo.DataIndexVo;
import com.shands.mod.main.service.feishu.ExecutiveCockpitService;
import com.shands.mod.main.util.DateUtils;
import com.shands.mod.util.BigDecimalUtils;
import com.shands.mod.vo.ResultVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @desc 飞书高管驾驶舱数据实现
 * @date 2023/4/17
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ExecutiveCockpitServiceImpl implements ExecutiveCockpitService {

  private final DataBoardIndexMapper dataBoardIndexMapper;

  private final DwsMapDistributionMapper dwsMapDistributionMapper;

  @Override
  public ResultVO<FeishuSummaryDataVo> summaryData() {
    FeishuSummaryDataVo feishuSummaryDataVo = new FeishuSummaryDataVo();
    Date maxDate = dataBoardIndexMapper.findMaxDate();
    setTotalRevenueAndDevelopMembership(feishuSummaryDataVo, maxDate);
    setHotelAndRoom(feishuSummaryDataVo);
    setRoomNight(feishuSummaryDataVo, maxDate);
    setCity(feishuSummaryDataVo, maxDate);
    return ResultVO.success(feishuSummaryDataVo);
  }

  private void setTotalRevenueAndDevelopMembership(FeishuSummaryDataVo feishuSummaryDataVo, Date maxDate) {
    Date curDate = maxDate;
    Date firstDatOfCurYear = DateUtil.beginOfYear(curDate);
    String endDate = DateUtil.format(curDate, DateUtils.DATE_FORMAT_YYYY_MM_DD_N);
    String startDate = DateUtil.format(firstDatOfCurYear, DateUtils.DATE_FORMAT_YYYY_MM_DD_N);
    BigDecimal totalRevenue = dataBoardIndexMapper.queryBlocTotalAmt(startDate, endDate).setScale(BigDecimalUtils.ROUNDING_SCALE_2, RoundingMode.HALF_UP);
    feishuSummaryDataVo.setTotalRevenue(totalRevenue);
    feishuSummaryDataVo.setDevelopMembership(dataBoardIndexMapper.queryBlocMemberF2F(startDate, endDate));
    feishuSummaryDataVo.setMembershipScale(dataBoardIndexMapper.queryBlocMemberF2F(null, null));
  }

  private void setHotelAndRoom(FeishuSummaryDataVo feishuSummaryDataVo) {
    HotelProjectMapBo hotelProjectMapBo = new HotelProjectMapBo();
    Date maxDate = dwsMapDistributionMapper.findMaxDate();
    DateTime dateTime = DateUtil.offsetDay(maxDate, -1);
    hotelProjectMapBo.setBizDate(maxDate);
    List<HotelProjectMapVo> hotelProjectMapVoList = dwsMapDistributionMapper.getHotelProjectMap(hotelProjectMapBo);
    int totalHotel = 0;
    int totalRoom = 0;
    for (HotelProjectMapVo item: hotelProjectMapVoList) {
      totalHotel += item.getHotelScale();
      totalRoom += item.getRoomScale();
    }
    HotelProjectMapBo yesterDayHotelProjectMapBo = new HotelProjectMapBo();
    yesterDayHotelProjectMapBo.setBizDate(dateTime);
    List<HotelProjectMapVo> yesterDayHotelProjectMapVoList = dwsMapDistributionMapper.getHotelProjectMap(yesterDayHotelProjectMapBo);
    int yesterdayTotalHotel = 0;
    int yesterdayTotalRoom = 0;
    for (HotelProjectMapVo item: yesterDayHotelProjectMapVoList) {
      yesterdayTotalHotel += item.getHotelScale();
      yesterdayTotalRoom += item.getRoomScale();
    }
    feishuSummaryDataVo.setTotalHotel(totalHotel);
    feishuSummaryDataVo.setTotalRoom(totalRoom);
    feishuSummaryDataVo.setHotelIncrease(totalHotel - yesterdayTotalHotel > 0);
    feishuSummaryDataVo.setRoomIncrease(totalRoom - yesterdayTotalRoom > 0);
  }

  private void setRoomNight(FeishuSummaryDataVo feishuSummaryDataVo, Date maxDate) {
    Date yesterday = maxDate;
    Date firstDatOfCurYear = DateUtil.beginOfYear(yesterday);
    String startDate = DateUtil.format(firstDatOfCurYear, DateUtils.DATE_FORMAT_YYYY_MM_DD_N);
    String endDate = DateUtil.format(yesterday, DateUtils.DATE_FORMAT_YYYY_MM_DD_N);
    RoomNightItem roomNightItem = dataBoardIndexMapper.queryRoomNight(startDate, endDate);
    feishuSummaryDataVo.setTotalRoomNight(roomNightItem.getRoomNight());
  }

  private void setCity(FeishuSummaryDataVo feishuSummaryDataVo, Date maxDate) {
    String bizDate = DateUtil.format(maxDate, DateUtils.DATE_FORMAT_YYYY_MM_DD_N);
    List<String> cities = dataBoardIndexMapper.queryCityNum(bizDate);
    Date beforeYesterday = DateUtil.offsetDay(maxDate, - 1);
    String beforeYesterdayStr = DateUtil.format(beforeYesterday, DateUtils.DATE_FORMAT_YYYY_MM_DD_N);
    List<String> beforeCities = dataBoardIndexMapper.queryCityNum(beforeYesterdayStr);
    feishuSummaryDataVo.setInvolvedCity(cities.size());
    feishuSummaryDataVo.setCityIncrease(cities.size() > beforeCities.size());
  }

  @Override
  public ResultVO<List<FeishuChannelRateItemVo>> channelRate() {
    Date yesterday = dataBoardIndexMapper.findMaxDate();
    String yesterdayDate = DateUtil.format(yesterday, DateUtils.DATE_FORMAT_YYYY_MM_DD_N);
    RoomNightItem roomNightItem = dataBoardIndexMapper.queryRoomNight(yesterdayDate, yesterdayDate);
    List<FeishuChannelRateItemVo> rateItemVos = new ArrayList<>();
    rateItemVos.add(new FeishuChannelRateItemVo("官渠", roomNightItem.getChannelWebRoomNight(), yesterdayDate));
    rateItemVos.add(new FeishuChannelRateItemVo("OTA", roomNightItem.getOtaRoomNight(), yesterdayDate));
    rateItemVos.add(new FeishuChannelRateItemVo("线下", roomNightItem.getOfflineRoomNight(), yesterdayDate));
    return ResultVO.success(rateItemVos);
  }

  @Override
  public ResultVO<FeishuMembershipContributeItemVo> memberContributeRate() {
    FeishuMembershipContributeItemVo membershipContributeItemVo = new FeishuMembershipContributeItemVo();
    Date nowDate = dataBoardIndexMapper.findMaxDate();
    DateTime firstDayOfCurYear = DateUtil.beginOfYear(nowDate);
    String startDate = DateUtil.format(firstDayOfCurYear, DateUtils.DATE_FORMAT_YYYY_MM_DD_N);
    String endDate = DateUtil.format(nowDate, DateUtils.DATE_FORMAT_YYYY_MM_DD_N);
    List<BigDecimal> memContributeRateOfMonthList = dataBoardIndexMapper.queryMemContributeRateOfMonth(startDate, endDate);
    List<GeneralDrawingVo> curYearDataList = Lists.newArrayList();
    for(int i = 0; i < memContributeRateOfMonthList.size(); i++) {
      GeneralDrawingVo drawingVo = new GeneralDrawingVo();
      drawingVo.setTime(i + 1 + "月");
      drawingVo.setNumber(memContributeRateOfMonthList.get(i).toString());
      curYearDataList.add(drawingVo);
    }
    membershipContributeItemVo.setCurYearDataList(curYearDataList);
    Date lastNowDate = DateUtil.offset(nowDate, DateField.YEAR, -1);
    DateTime firstDayOfLastYear = DateUtil.beginOfYear(lastNowDate);
    DateTime lastDayOfLastYear = DateUtil.endOfYear(lastNowDate);
    String lastStartDate = DateUtil.format(firstDayOfLastYear, DateUtils.DATE_FORMAT_YYYY_MM_DD_N);
    String lastEndDate = DateUtil.format(lastDayOfLastYear, DateUtils.DATE_FORMAT_YYYY_MM_DD_N);
    List<BigDecimal> lastYearMemContributeRateOfMonthList = dataBoardIndexMapper.queryMemContributeRateOfMonth(lastStartDate, lastEndDate);
    List<GeneralDrawingVo> lastYearDataList = Lists.newArrayList();
    for(int i = 0; i < lastYearMemContributeRateOfMonthList.size(); i++) {
      GeneralDrawingVo drawingVo = new GeneralDrawingVo();
      drawingVo.setTime(i + 1 + "月");
      drawingVo.setNumber(lastYearMemContributeRateOfMonthList.get(i).toString());
      lastYearDataList.add(drawingVo);
    }
    membershipContributeItemVo.setLastYearDataList(lastYearDataList);
    return ResultVO.success(membershipContributeItemVo);
  }

  @Override
  public ResultVO<FeishuInvestRentRoomPriceVo> investRentRate() {
    FeishuInvestRentRoomPriceVo rentRoomPriceVo = new FeishuInvestRentRoomPriceVo();
    Date yesterdayDate = dataBoardIndexMapper.findMaxDate();
    String yesterdayDateStr = DateUtil.format(yesterdayDate, DateUtils.DATE_FORMAT_YYYY_MM_DD);
    DataIndexVo homeDataBoardByAd = dataBoardIndexMapper.findHomeDataBoardByAds(
        DataTypeEnum.BLOC.name(), null, null, null, null, yesterdayDateStr, yesterdayDateStr, null).get(0);
    rentRoomPriceVo.setYesterdayValue(new BigDecimal(homeDataBoardByAd.getInvestOCC()).setScale(BigDecimalUtils.ROUNDING_SCALE_2, RoundingMode.HALF_UP));
    DateTime sevenDateTime = DateUtil.offsetDay(yesterdayDate, -6);
    String sevenDateTimeStr = DateUtil.format(sevenDateTime, DateUtils.DATE_FORMAT_YYYY_MM_DD);
    List<DataIndexVo> homeDataBoardByAds = dataBoardIndexMapper.findHomeDataBoardByAds(
        DataTypeEnum.BLOC.name(), null, null, null, null, sevenDateTimeStr, yesterdayDateStr, "YES");
    List<DayValueItem> recentValues = Lists.newArrayList();
    for (DataIndexVo item: homeDataBoardByAds) {
      BigDecimal value = new BigDecimal(item.getInvestOCC()).setScale(BigDecimalUtils.ROUNDING_SCALE_2, RoundingMode.HALF_UP);
      recentValues.add(new DayValueItem(value, item.getBizDate()));
    }
    rentRoomPriceVo.setRecentValueList(recentValues);
    rentRoomPriceVo.setMaxDate(yesterdayDateStr);
    return ResultVO.success(rentRoomPriceVo);
  }

  @Override
  public ResultVO<FeishuInvestRentRoomPriceVo> investAverageRoomPrice() {
    Date yesterdayDate = dataBoardIndexMapper.findMaxDate();
    String yesterdayDateStr = DateUtil.format(yesterdayDate, DateUtils.DATE_FORMAT_YYYY_MM_DD);
    DataIndexVo homeDataBoardByAd = dataBoardIndexMapper.findHomeDataBoardByAds(
        DataTypeEnum.BLOC.name(), null, null, null, null, yesterdayDateStr, yesterdayDateStr, null).get(0);
    FeishuInvestRentRoomPriceVo rentRoomPriceVo = new FeishuInvestRentRoomPriceVo();
    rentRoomPriceVo.setYesterdayValue(new BigDecimal(homeDataBoardByAd.getInvestARD()).setScale(BigDecimalUtils.ROUNDING_SCALE_2, RoundingMode.HALF_UP));
    DateTime sevenDateTime = DateUtil.offsetDay(yesterdayDate, -6);
    String sevenDateTimeStr = DateUtil.format(sevenDateTime, DateUtils.DATE_FORMAT_YYYY_MM_DD);
    List<DataIndexVo> homeDataBoardByAds = dataBoardIndexMapper.findHomeDataBoardByAds(
        DataTypeEnum.BLOC.name(), null, null, null, null, sevenDateTimeStr, yesterdayDateStr, "YES");
    List<DayValueItem> recentValues = Lists.newArrayList();
    for (DataIndexVo item: homeDataBoardByAds) {
      BigDecimal value = new BigDecimal(item.getInvestARD()).setScale(BigDecimalUtils.ROUNDING_SCALE_2, RoundingMode.HALF_UP);
      recentValues.add(new DayValueItem(value, item.getBizDate()));
    }
    rentRoomPriceVo.setRecentValueList(recentValues);
    rentRoomPriceVo.setMaxDate(yesterdayDateStr);
    return ResultVO.success(rentRoomPriceVo);
  }
}
