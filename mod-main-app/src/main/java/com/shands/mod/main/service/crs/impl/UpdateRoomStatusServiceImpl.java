package com.shands.mod.main.service.crs.impl;

import com.alibaba.fastjson.JSONObject;
import com.shands.mod.dao.mapper.hs.RoomOperateLogMapper;
import com.shands.mod.dao.model.req.hs.ModOperatrLogReq;
import com.shands.mod.dao.model.res.hs.ModOperateLogRes;
import com.shands.mod.main.service.crs.UpdateRoomStatusService;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class UpdateRoomStatusServiceImpl implements UpdateRoomStatusService {

  @Resource
  RoomOperateLogMapper logMapper;

  @Override
  public List<ModOperateLogRes> updateLog(
      ModOperatrLogReq req) {
    List<ModOperateLogRes> res = logMapper.updateLog(req);
    res.forEach(x -> {
      String result = x.getResult();
      JSONObject jsonObject = JSONObject.parseObject(result);
      String code = jsonObject.getString("code");
      String message = jsonObject.getString("message");
      if ("0".equals(code)) {
        x.setResult(message);
      } else {
        x.setResult("失败");
      }
    });
    return res;
  }
}
