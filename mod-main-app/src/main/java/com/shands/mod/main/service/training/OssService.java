package com.shands.mod.main.service.training;

import java.util.Map;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @Date 2022/8/29
 **/
public interface OssService {

  /**
   * @param file 文件
   * @param fileName 保存文件名称
   * @return
   */
  String uploadFile(MultipartFile file,String fileName,String type);

  /**
   * 确认上传
   */
  void verifyUpload(Map<Integer, String> files);

  /**
   * 将渠道客房价格图片上传至oss
   */
  String uploadImage2Oss(MultipartFile file,Integer ota);

  /**
   * 上传营业执照
   */
  String uploadBusinessLicense(MultipartFile file);

  /**
   * 显示酒店当天上传的各渠道图片
   */
  Map<String, String> dailyUploadedImgMap();

  /**
   * 上传合同附件
   */
  String uploadContractAttachment(MultipartFile file);
}
