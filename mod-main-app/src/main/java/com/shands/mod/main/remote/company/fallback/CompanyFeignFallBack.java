package com.shands.mod.main.remote.company.fallback;

import com.betterwood.base.common.model.Result;
import com.betterwood.base.common.util.ResultMessageUtil;
import com.ruoyi.common.core.web.domain.PageResult;
import com.shands.mod.dao.model.sales.tool.dto.VerifyInCompanyDto;
import com.shands.mod.dao.model.sales.tool.req.CompanyContractQo;
import com.shands.mod.dao.model.sales.tool.req.CompanyMemberPageListReq;
import com.shands.mod.dao.model.sales.tool.req.CompanyPageListReq;
import com.shands.mod.dao.model.sales.tool.req.VerifyInCompanyReq;
import com.shands.mod.dao.model.sales.tool.res.CompanyMemberPageListRes;
import com.shands.mod.dao.model.sales.tool.res.CompanyPageListRes;
import com.shands.mod.main.remote.company.CompanyFeignService;
import com.shands.mod.main.service.betterwood.rpc.resp.CompanyContractVO;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;
import java.util.List;

@Component
public class CompanyFeignFallBack implements FallbackFactory<CompanyFeignService> {

  @Override
  public CompanyFeignService create(Throwable cause) {
    return new CompanyFeignService() {

      @Override
      public Result<PageResult<CompanyPageListRes>> findByHotelCode(CompanyPageListReq qo) {
        return ResultMessageUtil.failResponse("查询企业列表失败");
      }

      @Override
      public Result<PageResult<CompanyPageListRes>> findCompanyBySalesId(CompanyPageListReq qo) {
        return ResultMessageUtil.failResponse("查询企业列表失败");
      }

      @Override
      public Result<List<VerifyInCompanyDto>> verifyInCompany(VerifyInCompanyReq qo) {
        return ResultMessageUtil.failResponse("校验企业会员是否在企业中失败");
      }

      @Override
      public Result<PageResult<CompanyMemberPageListRes>> findMemberByCompanyId(Long companyId, String param, Integer pageNo, Integer pageSize) {
        return ResultMessageUtil.failResponse("查询企业会员列表失败");
      }

      @Override
      public Result<CompanyContractVO> queryHotelContract(Long companyId, String hotelCode) {
        return ResultMessageUtil.failResponse("查询企业合同失败");
      }

      @Override
      public Result<CompanyContractVO> companyContractOrUpdate(CompanyContractQo qo) {
        return ResultMessageUtil.failResponse("新增或修改企业合同失败");
      }
    };

  }


}
