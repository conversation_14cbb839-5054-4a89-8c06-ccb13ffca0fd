package com.shands.mod.main.controller.crm;

import com.github.pagehelper.PageInfo;
import com.shands.mod.controller.BaseController;
import com.shands.mod.dao.model.req.hotel.GetAllOpenHotelReq;
import com.shands.mod.dao.model.res.hotel.OpenHotelRes;
import com.shands.mod.dao.model.syncuc.ModHotelInfo;
import com.shands.mod.main.service.syncuc.IModHotelInfoService;
import com.shands.mod.main.util.ThreadLocalHelper;
import com.shands.mod.vo.PageVO;
import com.shands.mod.vo.ResultVO;
import com.betterwood.log.core.enums.MethodTypeEnum;
import com.betterwood.log.core.annotation.ResultLog;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.validation.Valid;

@Slf4j
@RestController
@RequestMapping("/hotel")
public class ModHotelInfoController extends BaseController {


  @Autowired
  IModHotelInfoService infoService;


  @Override
  public boolean isPublic() {
    return true;
  }

  @PostMapping(
      value = "info",
      produces = {"application/json;charset=UTF-8"})
  @ApiOperation(value = "info", notes = "酒店信息", produces = "application/json")
    @ResultLog(name = "ModHotelInfoController.info", methodType = MethodTypeEnum.HTTP_UP)
  public ResultVO info() {
    ModHotelInfo info = infoService.queryById(ThreadLocalHelper.getCompanyId());
    return ResultVO.success(info);
  }

  @PostMapping(
      value = "editInfo",
      produces = {"application/json;charset=UTF-8"})
  @ApiOperation(value = "editInfo", notes = "修改酒店信息", produces = "application/json")
    @ResultLog(name = "ModHotelInfoController.editInfo", methodType = MethodTypeEnum.HTTP_UP)
  public ResultVO<Object> editInfo(@RequestBody ModHotelInfo modHotelInfo) {
    modHotelInfo.setHotelId(ThreadLocalHelper.getCompanyId());
    infoService.editInfo(modHotelInfo);
    return ResultVO.success();
  }

  @PostMapping(
      value = "companyList",
      produces = {"application/json;charset=UTF-8"})
  @ApiOperation(value = "companyList", notes = "根据集团id获得酒店列表", produces = "application/json")
    @ResultLog(name = "ModHotelInfoController.companyList", methodType = MethodTypeEnum.HTTP_UP)
  public ResultVO companyList(@RequestBody ModHotelInfo modHotelInfo, @RequestBody PageVO pageVO){
    return infoService.companyList(modHotelInfo,pageVO);
  }


  @PostMapping(
      value = "companyDetail",
      produces = {"application/json;charset=UTF-8"})
  @ApiOperation(value = "companyDetail", notes = "平台端酒店详情", produces = "application/json")
    @ResultLog(name = "ModHotelInfoController.detail", methodType = MethodTypeEnum.HTTP_UP)
  public ResultVO detail(@RequestBody Map<String,Integer> map) {
    Integer hotelId = map.get("hotelId");
    ModHotelInfo info = infoService.queryById(hotelId);
    return ResultVO.success(info);
  }

  @PostMapping(
      value = "updateCompany",
      produces = {"application/json;charset=UTF-8"})
  @ApiOperation(value = "updateCompany", notes = "修改酒店信息", produces = "application/json")
    @ResultLog(name = "ModHotelInfoController.updateCompany", methodType = MethodTypeEnum.HTTP_UP)
  public ResultVO<Object> updateCompany(@RequestBody ModHotelInfo modHotelInfo) {
    return ResultVO.success(infoService.update(modHotelInfo));
  }

  @GetMapping("/getAllOpen")
  @ApiOperation(value = "切换酒店-在营酒店分页列表查询")
  @ResultLog(name = "ModHotelInfoController.getAllOpen", methodType = MethodTypeEnum.HTTP_UP)
  public ResultVO<PageInfo<OpenHotelRes>> getAllOpen(@Valid GetAllOpenHotelReq req) {
    return ResultVO.success(infoService.getAllOpenHotel(req));
  }

}
