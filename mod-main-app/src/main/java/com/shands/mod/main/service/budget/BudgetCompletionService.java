package com.shands.mod.main.service.budget;

import cn.hutool.core.date.DateUtil;
import com.delonix.bi.dao.mapper.AdsTradeDlEcoAppHotelWideDMapper;
import com.delonix.bi.dao.model.AdsTradeDlEcoAppHotelWideD;
import com.delonix.bi.dao.model.AdsTradeDlEcoAppHotelWideDExample;
import com.shands.mod.dao.model.enums.*;
import com.shands.mod.dao.model.res.BudgetCompletionDTO;
import com.shands.mod.dao.model.res.BudgetCompletionRes;
import com.shands.mod.dao.model.syncuc.ModHotelInfo;
import com.shands.mod.exception.NormalException;
import com.shands.mod.main.config.BetterwoodConfig;
import com.shands.mod.main.service.common.HotelInfoCommonService;
import com.shands.mod.main.service.common.UserInfoCommonService;
import com.shands.mod.main.util.ThreadLocalHelper;
import com.shands.mod.main.util.hs.DateUtils;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.*;
import java.time.*;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 预算完成情况服务
 */
@Service
public class BudgetCompletionService {

  @Autowired
  private AdsTradeDlEcoAppHotelWideDMapper adsTradeDlEcoAppHotelWideDMapper;

  @Autowired
  private HotelInfoCommonService hotelInfoCommonService;

  @Autowired
  private UserInfoCommonService userInfoCommonService;

  @Autowired
  private BetterwoodConfig betterwoodConfig;

  // 预算完成情况菜单编码
  private static final String MENU_CODE_BUDGET = "budget_completion";

  /**
   * 获取预算完成情况
   */
  public BudgetCompletionRes getBudgetCompletion() throws NormalException {

    // 1. 获取用户权限
    List<String> param = userInfoCommonService.getUserRights(
        ThreadLocalHelper.getUser().getId(),
        ThreadLocalHelper.getCompanyId(),
        UserRightsTypeEnum.APP);

    // 2. 校验用户权限
    validateUserPermissions(param);

    // 3. 获取酒店code
    ModHotelInfo modHotelInfo = hotelInfoCommonService.findHotelInfo(ThreadLocalHelper.getCompanyId());
    if(modHotelInfo == null){
      throw new NormalException("酒店信息查询为空");
    }

    return BudgetCompletionRes.builder()
            .monthData(getMonthlyBudgetCompletion(modHotelInfo.getHotelCode()))
            .quarterData(getQuarterlyBudgetCompletion(modHotelInfo.getHotelCode()))
            .isHotelOpen(HotelStatusEnum.OPEN.getCode().equals(modHotelInfo.getContract()))
            .build();
  }

  private void validateUserPermissions(List<String> param) throws NormalException {
    if (CollectionUtils.isEmpty(param) || !param.contains(MENU_CODE_BUDGET)) {
      throw new NormalException("用户权限不足,请联系管理员！");
    }
  }

  /**
   * 格式化金额
   * 大于10000转换为万为单位，保留2位小数，使用千分位分隔
   */
  private String formatAmount(BigDecimal amount) {
    if (amount == null || amount.compareTo(BigDecimal.ZERO) == 0) {
      return "0元";
    }

    // 获取绝对值
    BigDecimal absAmount = amount.abs();
    DecimalFormat df = new DecimalFormat("#,##0.##");

    // 判断绝对值是否大于等于10000
    if (absAmount.compareTo(new BigDecimal("10000")) >= 0) {
      BigDecimal result = amount.divide(new BigDecimal("10000"), 2, RoundingMode.HALF_UP);
      return df.format(result.stripTrailingZeros()) + "万";
    }

    return df.format(amount.stripTrailingZeros()) + "元";
  }

  /**
   * 计算时间进度百分比
   */
  private String calculateTimeProgress(int currentDays, int totalDays) {
    if (totalDays == 0) {
      return "0";
    }
    return new BigDecimal(currentDays)
        .divide(new BigDecimal(totalDays), 4, RoundingMode.HALF_UP)
        .multiply(new BigDecimal("100"))
        .setScale(2, RoundingMode.HALF_UP)
        .toString();
  }

  /**
   * 计算完成率
   */
  private String calculateCompletionRate(BigDecimal actual, BigDecimal target) {
    if (target == null || target.compareTo(BigDecimal.ZERO) == 0) {
      return "0";
    }
    return actual.divide(target, 4, RoundingMode.HALF_UP)
            .multiply(new BigDecimal("100"))
            .setScale(2, RoundingMode.HALF_UP)
            .stripTrailingZeros()
            .toPlainString();
  }

  /**
   * 计算差值比例
   */
  private String calculateDiffRate(BigDecimal diff, BigDecimal target) {
    if (target == null || target.compareTo(BigDecimal.ZERO) == 0) {
      return "0%";
    }
    return diff.divide(target, 4, RoundingMode.HALF_UP)
            .multiply(new BigDecimal("100"))
            .setScale(2, RoundingMode.HALF_UP)
            .stripTrailingZeros()
            .toPlainString() + "%";
  }

  /**
   * 格式化数字（添加千分位）
   */
  private String formatNumber(Number number) {
    if (number == null) {
      return "0";
    }
    DecimalFormat df = new DecimalFormat("#,##0");
    return df.format(number);
  }

  /**
   * 格式化差距率
   */
  private String formatDiffRate(BigDecimal rate) {
    return rate.multiply(new BigDecimal("100"))
            .setScale(2, RoundingMode.HALF_UP)
            .stripTrailingZeros()
            .toPlainString() + "%";
  }

  /**
   * 获取月度预算完成情况
   */
  private BudgetCompletionDTO getMonthlyBudgetCompletion(String hotelCode) {
    // 当前查询时间
    LocalDate today = Optional.ofNullable(betterwoodConfig.getQueryDate())
            .filter(dateStr -> !dateStr.trim().isEmpty())
            .map(String::trim)
            .map(LocalDate::parse)
            .orElse(LocalDate.now());

    // 如果是1号，查询上一个月的数据
    LocalDate queryMonth = today.getDayOfMonth() == 1 ? today.minusMonths(1) : today.minusDays(1);
    LocalDate queryStartDate = queryMonth.withDayOfMonth(1);
    Date queryDate = Date.from(queryStartDate.atStartOfDay(ZoneId.systemDefault()).toInstant());

    AdsTradeDlEcoAppHotelWideDExample example = new AdsTradeDlEcoAppHotelWideDExample();
    example.createCriteria()
            .andHotelCodeEqualTo(hotelCode)
            .andBizMonthEqualTo(queryDate);
    List<AdsTradeDlEcoAppHotelWideD> budgetHList = adsTradeDlEcoAppHotelWideDMapper.selectByExample(example);

    AdsTradeDlEcoAppHotelWideD hotelBudgetH = budgetHList.stream().findFirst()
            .orElse(AdsTradeDlEcoAppHotelWideD.builder().build());

    BigDecimal income = Optional.ofNullable(hotelBudgetH.getTotalAmt()).map(BigDecimal::valueOf).orElse(BigDecimal.ZERO);
    BigDecimal targetIncome = Optional.ofNullable(hotelBudgetH.getBudgetTotalAmt()).map(BigDecimal::valueOf).orElse(BigDecimal.ZERO);
    Integer cards = Optional.ofNullable(hotelBudgetH.getCompanyCardN()).map(Long::intValue).orElse(0);
    Integer newAppMembers = Optional.ofNullable(hotelBudgetH.getAppAddMemN()).map(Long::intValue).orElse(0);
    Date updateTime = DateUtil.offsetDay(Date.from(today.atStartOfDay(ZoneId.systemDefault()).toInstant()), -1);

    // 计算时间进度
    String timeProgress;
    if (today.getDayOfMonth() == 1) {
      timeProgress = "100"; // 查询的是上个月，时间进度固定 100%
    } else {
      int currentDay = queryMonth.getDayOfMonth();
      int totalDays = YearMonth.from(queryMonth).lengthOfMonth();
      timeProgress = calculateTimeProgress(currentDay, totalDays);
    }

    // 计算时间进度率（转为BigDecimal便于计算）
    BigDecimal timeProgressRate = new BigDecimal(timeProgress).divide(new BigDecimal("100"), 4, RoundingMode.HALF_UP);

    // 计算当前完成率
    BigDecimal completionRate = BigDecimal.ZERO;
    if (targetIncome.compareTo(BigDecimal.ZERO) > 0) {
      completionRate = income.divide(targetIncome, 4, RoundingMode.HALF_UP);
    }

    // 计算差距（当前完成率-时间进度率）
    BigDecimal diffRate = completionRate.subtract(timeProgressRate);

    // 计算差值金额（当前完成-目标*时间进度率）
    BigDecimal diffAmount = income.subtract(targetIncome.multiply(timeProgressRate));

    return BudgetCompletionDTO.builder()
            .updateTime(DateUtils.dateToString(updateTime, DateUtils.DATE_FORMAT_MM_DD))
            .period(PeriodEnum.MONTH.getCode())
            .title(BudgetLabelEnum.MONTH_TITLE.getValue())
            .incomeLabel(BudgetLabelEnum.INCOME_LABEL.getValue())
            .income(formatAmount(income))
            .targetIncomeLabel(BudgetLabelEnum.TARGET_INCOME_LABEL.getValue())
            .targetIncome(formatAmount(targetIncome))
            .completionLabel(BudgetLabelEnum.COMPLETION_LABEL.getValue())
            .completionRate(calculateCompletionRate(income, targetIncome))
            .cardLabel(BudgetLabelEnum.CARD_LABEL.getValue())
            .cards(formatNumber(cards))
            .memberLabel(BudgetLabelEnum.MEMBER_LABEL.getValue())
            .newAppMembers(formatNumber(newAppMembers))
            .diffLabel(BudgetLabelEnum.DIFF_LABEL.getValue())
            .diffAmount(formatAmount(diffAmount))
            .diffRate(formatDiffRate(diffRate))
            .timeLabel(BudgetLabelEnum.TIME_LABEL.getValue())
            .timeProgress(timeProgress)
            .build();
  }


  /**
   * 获取季度预算完成情况
   */
  /**
   * 获取季度预算完成情况
   */
  private BudgetCompletionDTO getQuarterlyBudgetCompletion(String hotelCode) {
    // 获取当前时间（可能由配置指定）
    LocalDate today = Optional.ofNullable(betterwoodConfig.getQueryDate())
            .filter(dateStr -> !dateStr.trim().isEmpty())
            .map(String::trim)
            .map(LocalDate::parse)
            .orElse(LocalDate.now());

    // 判断是否为季度第一个月的第一天
    LocalDate queryDate;
    if (today.getDayOfMonth() == 1 && today.getMonthValue() % 3 == 1) {
      // 如果是季度第一个月(1,4,7,10月)的第一天，获取上个季度
      queryDate = today.minusMonths(3);
    } else {
      // 其他情况（包括非季度第一个月的1号）获取本季度,时间查询t-1
      today = today.minusDays(1);
      queryDate = today;
    }
    int currentQuarter = (queryDate.getMonthValue() - 1) / 3 + 1;
    LocalDate quarterStart = queryDate.withMonth((currentQuarter - 1) * 3 + 1).withDayOfMonth(1);
    LocalDate quarterEnd = quarterStart.plusMonths(3); // 不含末日

    Date queryStartTime = Date.from(quarterStart.atStartOfDay(ZoneId.systemDefault()).toInstant());
    Date queryEndTime = Date.from(quarterEnd.atStartOfDay(ZoneId.systemDefault()).toInstant());

    AdsTradeDlEcoAppHotelWideDExample example = new AdsTradeDlEcoAppHotelWideDExample();
    example.createCriteria()
            .andHotelCodeEqualTo(hotelCode)
            .andBizMonthGreaterThanOrEqualTo(queryStartTime)
            .andBizMonthLessThan(queryEndTime);
    example.setOrderByClause("data_time DESC");
    List<AdsTradeDlEcoAppHotelWideD> budgetHList = adsTradeDlEcoAppHotelWideDMapper.selectByExample(example);

    // 汇总季度数据
    BigDecimal income = budgetHList.stream()
            .map(budget -> Optional.ofNullable(budget.getTotalAmt()).orElse(0.0))
            .map(BigDecimal::valueOf)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

    BigDecimal targetIncome = budgetHList.stream()
            .map(budget -> Optional.ofNullable(budget.getBudgetTotalAmt()).orElse(0.0))
            .map(BigDecimal::valueOf)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

    Integer cards = budgetHList.stream()
            .map(budget -> Optional.ofNullable(budget.getCompanyCardN()).map(Long::intValue).orElse(0))
            .reduce(0, Integer::sum);

    Integer newAppMembers = budgetHList.stream()
            .map(budget -> Optional.ofNullable(budget.getAppAddMemN()).map(Long::intValue).orElse(0))
            .reduce(0, Integer::sum);

    Date updateTime = DateUtil.offsetDay(Date.from(today.atStartOfDay(ZoneId.systemDefault()).toInstant()), -1);

    // 时间进度计算
    String timeProgress;
    if (today.getDayOfMonth() == 1) {
      timeProgress = "100"; // 查的是上个季度
    } else {
      int currentDay = (int) ChronoUnit.DAYS.between(quarterStart, today) + 1;
      int totalDays = (int) ChronoUnit.DAYS.between(quarterStart, quarterEnd);
      timeProgress = calculateTimeProgress(currentDay, totalDays);
    }

    // 计算时间进度率（转为BigDecimal便于计算）
    BigDecimal timeProgressRate = new BigDecimal(timeProgress).divide(new BigDecimal("100"), 4, RoundingMode.HALF_UP);

    // 计算当前完成率
    BigDecimal completionRate = BigDecimal.ZERO;
    if (targetIncome.compareTo(BigDecimal.ZERO) > 0) {
      completionRate = income.divide(targetIncome, 4, RoundingMode.HALF_UP);
    }

    // 计算差距（当前完成率-时间进度率）
    BigDecimal diffRate = completionRate.subtract(timeProgressRate);

    // 计算差值金额（当前完成-目标*时间进度率）
    BigDecimal diffAmount = income.subtract(targetIncome.multiply(timeProgressRate));

    return BudgetCompletionDTO.builder()
            .updateTime(DateUtils.dateToString(updateTime, DateUtils.DATE_FORMAT_MM_DD))
            .period(PeriodEnum.QUARTERLY.getCode())
            .title(BudgetLabelEnum.QUARTERLY_TITLE.getValue())
            .incomeLabel(BudgetLabelEnum.INCOME_LABEL.getValue())
            .income(formatAmount(income))
            .targetIncomeLabel(BudgetLabelEnum.TARGET_INCOME_LABEL.getValue())
            .targetIncome(formatAmount(targetIncome))
            .completionLabel(BudgetLabelEnum.COMPLETION_LABEL.getValue())
            .completionRate(calculateCompletionRate(income, targetIncome))
            .cardLabel(BudgetLabelEnum.CARD_LABEL.getValue())
            .cards(formatNumber(cards))
            .memberLabel(BudgetLabelEnum.MEMBER_LABEL.getValue())
            .newAppMembers(formatNumber(newAppMembers))
            .diffLabel(BudgetLabelEnum.DIFF_LABEL.getValue())
            .diffAmount(formatAmount(diffAmount))
            .diffRate(formatDiffRate(diffRate))
            .timeLabel(BudgetLabelEnum.TIME_LABEL.getValue())
            .timeProgress(timeProgress)
            .build();
  }


}