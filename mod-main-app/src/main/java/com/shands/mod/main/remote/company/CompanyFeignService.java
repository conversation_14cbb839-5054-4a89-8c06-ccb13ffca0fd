package com.shands.mod.main.remote.company;

import com.betterwood.base.common.model.Result;
import com.betterwood.base.common.util.ResultMessageUtil;
import com.betterwood.log.core.annotation.ResultLog;
import com.betterwood.log.core.enums.MethodTypeEnum;
import com.ruoyi.common.core.web.domain.PageResult;
import com.shands.mod.config.FeignConfigure;
import com.shands.mod.dao.model.sales.tool.dto.VerifyInCompanyDto;
import com.shands.mod.dao.model.sales.tool.req.CompanyContractQo;
import com.shands.mod.dao.model.sales.tool.req.CompanyMemberPageListReq;
import com.shands.mod.dao.model.sales.tool.req.CompanyPageListReq;
import com.shands.mod.dao.model.sales.tool.req.VerifyInCompanyReq;
import com.shands.mod.dao.model.sales.tool.res.CompanyMemberPageListRes;
import com.shands.mod.dao.model.sales.tool.res.CompanyPageListRes;
import com.shands.mod.main.remote.member.fallback.CompanyMemberFeignFallBack;
import com.shands.mod.main.service.betterwood.rpc.resp.CompanyContractVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;

@FeignClient(name = "bdw-company", fallback = CompanyMemberFeignFallBack.class, configuration = FeignConfigure.class)
public interface CompanyFeignService {


  @ApiOperation(value = "根据酒店code查询发展或签约企业")
  @PostMapping("/company/find/by_hotel_code")
  Result<PageResult<CompanyPageListRes>> findByHotelCode(@RequestBody @Valid CompanyPageListReq qo);



  @ApiOperation(value = "根据销售员查询发展或签约企业企业")
  @PostMapping("/company/find/by_sales_id")
  Result<PageResult<CompanyPageListRes>> findCompanyBySalesId(@RequestBody @Valid CompanyPageListReq qo);


  @PostMapping("/company/member/verifyInCompany")
  @ApiOperation(value = "校验企业会员是否在企业中")
  Result<List<VerifyInCompanyDto>> verifyInCompany(@RequestBody @Valid VerifyInCompanyReq qo);

  @ApiOperation(value = "根据companyId查询所有会员")
  @GetMapping("/company/member/find/member/by_company_id")
  Result<PageResult<CompanyMemberPageListRes>> findMemberByCompanyId(@RequestParam("companyId") Long companyId,
                                                                    @RequestParam("param") String param,
                                                                    @RequestParam("pageNo") Integer pageNo,
                                                                    @RequestParam("pageSize") Integer pageSize);


  @GetMapping("/company/contract/query")
  @ApiOperation(value = "单店协议酒店合同查询")
  Result<CompanyContractVO> queryHotelContract(@RequestParam("companyId") Long companyId, @RequestParam("hotelCode") String hotelCode);

  @PostMapping("/company/contract/addOrUpdate")
  @ApiOperation(value = "单店协议酒店合同新增or更新")
  Result<CompanyContractVO> companyContractOrUpdate(@RequestBody @Valid CompanyContractQo qo);
}
