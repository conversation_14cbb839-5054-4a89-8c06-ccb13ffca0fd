package com.shands.mod.main.service.betterwood.rpc;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.betterwood.base.common.exception.BdwServiceException;
import com.betterwood.base.common.model.PageVO;
import com.betterwood.base.common.model.Result;
import com.betterwood.log.core.annotation.ResultLog;
import com.betterwood.log.core.enums.MethodTypeEnum;
import com.github.pagehelper.PageInfo;
import com.ruoyi.common.core.web.domain.PageResult;
import com.shands.mod.dao.model.developmember.DevelopmentMemberBo;
import com.shands.mod.dao.model.developmember.DevelopmentMemberVo;
import com.shands.mod.dao.model.res.elsreport.BdxQurMemberInfoBo;
import com.shands.mod.dao.model.sales.tool.req.AddOrUpdateCompanyReq;
import com.shands.mod.dao.model.sales.tool.req.CompanyApplyReq;
import com.shands.mod.dao.model.sales.tool.req.CompanyContractQo;
import com.shands.mod.dao.model.sales.tool.req.CompanyMemberPageListReq;
import com.shands.mod.dao.model.sales.tool.req.CompanyPageListReq;
import com.shands.mod.dao.model.sales.tool.req.CompanyProtocolReq;
import com.shands.mod.dao.model.sales.tool.res.CompanyMemberPageListRes;
import com.shands.mod.dao.model.sales.tool.res.CompanyPageListRes;
import com.shands.mod.dao.model.syncuc.ModUserCode;
import com.shands.mod.exception.ServiceException;
import com.shands.mod.external.model.dto.QRCodeDto;
import com.shands.mod.external.model.vo.ActivityShowPosterVo;
import com.shands.mod.external.model.vo.DevelopmentQrCodeVo;
import com.shands.mod.main.config.BetterwoodConfig;
import com.shands.mod.main.config.DevelopSwitchConfig;
import com.shands.mod.main.enums.CompanyContractEnum;
import com.shands.mod.main.enums.MemberStatusEnum;
import com.shands.mod.main.remote.company.CompanyFeignService;
import com.shands.mod.main.remote.roomPrice.RoomPriceRemoteService;
import com.shands.mod.main.service.betterwood.resp.AreaCodeResp;
import com.shands.mod.main.service.betterwood.rpc.req.SalesmanQueryReq;
import com.shands.mod.main.service.betterwood.rpc.resp.*;
import com.shands.mod.main.util.HttpClientUtil;
import com.shands.mod.main.vo.feign.RpcHttpResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.shands.mod.vo.ResultCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * http调用百达星接口封装层
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class BdxRpcService {


  @Autowired
  private DevelopSwitchConfig developSwitchConfig;

  @Autowired
  private BetterwoodConfig betterwoodConfig;

  @Autowired
  private CompanyFeignService companyFeignService;

  @Autowired
  private RoomPriceRemoteService roomPriceRemoteService;

  @Value("${els.h5url:https://m.kaiyuanhotels.com}")
  private String sxeH5Url;

  private static final String COMPANY_CONTRACT_H5_PAGE = "/sxe?companyId=";

  @ResultLog(name = "BdxRpcService.getInviteCodeByUcId", methodType = MethodTypeEnum.HTTP_UP)
  public ModUserCode getInviteCodeByUcId(Integer ucId) {
    String url = developSwitchConfig.getBetterwoodFindCodeByUcIdUrl();
    ModUserCode data = null;
    Map<String, String> params = new HashMap<>();
    params.put("ucId", Integer.toString(ucId));
    try {
      JSONObject jsonObject = HttpClientUtil.doGet(url, null, params);
      if (jsonObject != null && jsonObject.get("data") != null) {
        data = JSONUtil.toBean(jsonObject.get("data").toString(), ModUserCode.class);
      }
    } catch (Exception e) {
      log.error("百达星查询错误 getInviteCodeByUcId {}", JSONUtil.toJsonStr(ucId), e);
    }
    return data;
  }

  @ResultLog(name = "BdxRpcService.getInviteCode", methodType = MethodTypeEnum.HTTP_UP)
  public String getInviteCode(Integer ucId) {
    String url = developSwitchConfig.getBetterwoodGetInviteCodeUrl();
    String data = "";
    Map<String, Object> params = new HashMap<>();
    params.put("ucId", ucId);
    try {
      JSONObject jsonObject = HttpClientUtil.doJsonPost(url, null, params);
      if (jsonObject != null && jsonObject.get("data") != null) {
        data = jsonObject.get("data").toString();
      }
    } catch (Exception e) {
      log.error("百达星查询错误 getInviteCode {}", JSONUtil.toJsonStr(ucId), e);
    }
    if (StrUtil.isEmpty(data)) {
      throw new ServiceException("bdx获取邀请码错误 ");
    }
    return data;
  }

  @ResultLog(name = "BdxRpcService.getMiniAppMemberCode", methodType = MethodTypeEnum.HTTP_UP)
  public DevelopmentQrCodeVo getMiniAppMemberCode(QRCodeDto qrCodeDto) {
    String url = developSwitchConfig.getBetterwoodGetWxCodeUrl();
    String requestBody = JSONUtil.toJsonStr(qrCodeDto);
    try {
      RpcHttpResponse httpResponse = HttpClientUtil.doPost(url, requestBody);
      log.info("BdxRpcService.getMiniAppMemberCode,url:{},params:{},jsonObject:{}",url,JSONObject.toJSONString(requestBody),httpResponse);
      if (ObjectUtil.isNull(httpResponse) || !httpResponse.isOk()) {
        return null;
      }
      JSONObject jsonObject = JSONObject.parseObject(httpResponse.getBody());
      if (jsonObject.get("data") != null) {
        return JSONUtil.toBean(jsonObject.get("data").toString(), DevelopmentQrCodeVo.class);
      }
    } catch (Exception e) {
      log.error("百达星查询错误 getMiniAppMemberCode {}", JSONUtil.toJsonStr(qrCodeDto), e);
    }
    return null;
  }

  /**
   * 通过员工uc_id查url_id
   *
   * @param ucId
   * @param type 1扫码住 2百达卡
   * @param cardId 百达卡id
   * @return
   */
  @ResultLog(name = "BdxRpcService.getCodeUrl", methodType = MethodTypeEnum.HTTP_UP)
  public String getCodeUrlId(String ucId, Integer type, Integer cardId) {
    String url = developSwitchConfig.getBetterwoodGetCodeUrl();
    Map<String, Object> params = new HashMap<>();
    params.put("ucId", ucId);
    params.put("type", type);
    params.put("cardId", cardId);
    String requestBody = JSONUtil.toJsonStr(params);
    try {
      RpcHttpResponse httpResponse = HttpClientUtil.doPost(url, requestBody);
      log.info("BdxRpcService.getCodeUrl,url:{},params:{},jsonObject:{}", url,
          JSONObject.toJSONString(requestBody), httpResponse);
      if (ObjectUtil.isNull(httpResponse) || !httpResponse.isOk()) {
        log.error("HTTP 请求失败，url: {}, params: {}, response: {}", url, JSONObject.toJSONString(params), httpResponse);
        return null;
      }
      // 解析 JSON 响应
      JSONObject jsonObject = JSONObject.parseObject(httpResponse.getBody());

      // 获取数据部分
      JSONObject data = jsonObject.getJSONObject("data");
      if (data != null && data.containsKey("urlId")) {
        return data.getString("urlId"); // 返回urlId
      }
    } catch (Exception e) {
      log.error("BdxRpcService.getCodeUrl 错误 {}", JSONUtil.toJsonStr(params), e);
    }
    return null;
  }

  @ResultLog(name = "BdxRpcService.getDevelopmentMemberList", methodType = MethodTypeEnum.HTTP_UP)
  public DevelopmentMemberVo getDevelopmentMemberList(DevelopmentMemberBo developmentMemberBo) {
    String url = developSwitchConfig.getBetterwoodGetDevelopRecordUrl();
    String requestBody = JSONUtil.toJsonStr(developmentMemberBo);
    try {
      RpcHttpResponse httpResponse = HttpClientUtil.doPost(url, requestBody);
      if (ObjectUtil.isNull(httpResponse) || !httpResponse.isOk()) {
        return null;
      }
      JSONObject jsonObject = JSONObject.parseObject(httpResponse.getBody());
      if (jsonObject.get("data") != null) {
        return JSONUtil.toBean(jsonObject.get("data").toString(), DevelopmentMemberVo.class);
      }
    } catch (Exception e) {
      log.error("百达星查询错误 getDevelopmentMemberList {}",
          JSONUtil.toJsonStr(developmentMemberBo), e);
    }
    return null;

  }



  @ResultLog(name = "BdxRpcService.getDevelopmentMemberListV2", methodType = MethodTypeEnum.HTTP_UP)
  public DevelopmentMemberVo getDevelopmentMemberListV2(DevelopmentMemberBo developmentMemberBo) {
    String url = developSwitchConfig.getBetterwoodGetDevelopRecordUrlV2();
    String requestBody = JSONUtil.toJsonStr(developmentMemberBo);
    try {
      RpcHttpResponse httpResponse = HttpClientUtil.doPost(url, requestBody);
      if (ObjectUtil.isNull(httpResponse) || !httpResponse.isOk()) {
        return null;
      }
      JSONObject jsonObject = JSONObject.parseObject(httpResponse.getBody());
      if (jsonObject.get("data") != null) {
        return JSONUtil.toBean(jsonObject.get("data").toString(), DevelopmentMemberVo.class);
      }
    } catch (Exception e) {
      log.error("百达星查询错误 getDevelopmentMemberList {}",
          JSONUtil.toJsonStr(developmentMemberBo), e);
    }
    return null;

  }

  @ResultLog(name = "BdxRpcService.qurMemberInfo", methodType = MethodTypeEnum.HTTP_UP)
  public JSONObject qurMemberInfo(BdxQurMemberInfoBo bdxQurMemberInfoBo) {
    String url = betterwoodConfig.getBetterwoodPlatformUrl()
        + "/open-api/user-center/query_bdx_user_info_develop";
    String requestBody = JSONUtil.toJsonStr(bdxQurMemberInfoBo);
    try {
      RpcHttpResponse httpResponse = HttpClientUtil.doPost(url, requestBody);
      if (ObjectUtil.isNull(httpResponse) || !httpResponse.isOk()) {
        return null;
      }
      return JSONObject.parseObject(httpResponse.getBody()).getJSONObject("data");
    } catch (Exception e) {
      log.error("百达星查询错误 qurMemberInfo {}", JSONUtil.toJsonStr(bdxQurMemberInfoBo), e);
    }
    return null;


  }

  public List<AreaCodeResp> qurAreaCode() {
    String url = betterwoodConfig.getBetterwoodPlatformUrl() + "/open-api/user-center/getAreaCode";
    try {
      JSONObject jsonObject = HttpClientUtil.doGet(url, null,null);
      if (jsonObject.get("data") != null) {
        return JSONUtil.toList(jsonObject.get("data").toString(), AreaCodeResp.class);
      }
    } catch (Exception e) {
      log.error("百达星查询错误 qurAreaCode", e);
    }
    return null;
  }



  @ResultLog(name = "BdxRpcService.getActivityShowPoster", methodType = MethodTypeEnum.HTTP_UP)
  public List<ActivityShowPosterVo> getActivityShowPoster(String ucId) {
    String url = developSwitchConfig.getBetterwoodGetActivityShowPosterUrl();
    Map<String, String> params = new HashMap<>();
    params.put("ucId", ucId);
    try {
      JSONObject jsonObject = HttpClientUtil.doGet(url, null, params);
      log.info("BdxRpcService.getActivityShowPoster,url:{},params:{},jsonObject:{}",url,JSONObject.toJSONString(params),jsonObject);
      if (jsonObject != null && jsonObject.get("data") != null) {
        return JSONUtil.toList(jsonObject.get("data").toString(), ActivityShowPosterVo.class);
      }
    } catch (Exception e) {
      log.error("百达星查询错误 getActivityShowPoster {}", JSONUtil.toJsonStr(ucId), e);
    }
    return null;
  }

  /**
   * 工单状态变更通知
   * @return
   */
  @ResultLog(name = "BdxRpcService.getActivityShowPoster", methodType = MethodTypeEnum.HTTP_UP)
  public void orderStatusChangeNotify(String orderSn, Integer status) {
    String url = developSwitchConfig.getBetterwoodOrderStatusChangeNotifyUrl();
    Map<String, Object> params = new HashMap<>();
    params.put("sxeOrderNo", orderSn);
    params.put("status",status);
    String requestBody = JSONUtil.toJsonStr(params);
    try {
      RpcHttpResponse httpResponse = HttpClientUtil.doPost(url, requestBody);
      if (ObjectUtil.isNull(httpResponse) || !httpResponse.isOk()) {
        log.error("工单状态变更通知失败，返回结果:{}", JSONUtil.toJsonStr(httpResponse));
      }
    } catch (Exception e) {
      log.error("工单状态变更通知失败", e);
    }
  }

  @ResultLog(name = "BdxRpcService.getCompanyList", methodType = MethodTypeEnum.HTTP_UP)
  public PageInfo<CompanyPageListRes> getCompanyList(CompanyPageListReq req) {
    String url = betterwoodConfig.getBetterwoodPlatformUrl() + "/open-api/member/find/company/by_hotel_code";
    Map<String, String> params = new HashMap<>();
    params.put("hotelCode", req.getHotelCode());
    params.put("param", req.getParam());
    params.put("pageNo", req.getPageNo().toString());
    params.put("pageSize", req.getPageSize().toString());
    try {
      JSONObject jsonObject = HttpClientUtil.doGet(url, null, params);
      if (jsonObject != null && jsonObject.getJSONObject("data") != null
          && StrUtil.isNotBlank(jsonObject.getJSONObject("data").getJSONArray("rows").toString())) {
        JSONObject data =  jsonObject.getJSONObject("data");
        List<CompanyPageListRes> records = JSONUtil.toList(data.getJSONArray("rows").toString(), CompanyPageListRes.class);
        Integer totalPage = data.getInteger("totalPage");
        Integer totalRows = data.getInteger("totalRows");
        PageInfo<CompanyPageListRes> pageRes = new PageInfo<>(records);
        pageRes.setTotal(totalRows);
        pageRes.setPages(totalPage);
        return pageRes;
      }
    } catch (Exception e) {
      log.error("百达屋查询错误 getCompanyList", e);
    }
    return new PageInfo<>();
  }

  @ResultLog(name = "BdxRpcService.getCompanyListByHotelCode", methodType = MethodTypeEnum.HTTP_UP)
  public PageInfo<CompanyPageListRes> getCompanyListByHotelCode(CompanyPageListReq req) {

    Result<PageResult<CompanyPageListRes>> findCompanyFeignResult = companyFeignService.findByHotelCode(req);

    if (findCompanyFeignResult == null || !findCompanyFeignResult.isOk()) {
      throw new RuntimeException("企业查询失败，请稍后再试");
    }
    PageResult<CompanyPageListRes> companyPage = findCompanyFeignResult.getData();
    companyPage.getRows().forEach(company -> {
      if (StrUtil.startWith(company.getTaxpayerId(), "1")) {
        company.setShowSMSChoice(true);
      }
      company.setContractStateLabel(CompanyContractEnum.getDesc(company.getContractState()));
      company.setUrl(sxeH5Url + COMPANY_CONTRACT_H5_PAGE +  company.getCompanyId());
    });

    PageInfo<CompanyPageListRes> pageRes = new PageInfo<>(companyPage.getRows());
    pageRes.setTotal(companyPage.getTotalRows());
    pageRes.setPages(companyPage.getTotalPage());
    return pageRes;
  }

  @ResultLog(name = "BdxRpcService.getCompanyListBySalesId", methodType = MethodTypeEnum.HTTP_UP)
  public PageInfo<CompanyPageListRes> getCompanyListBySalesId(CompanyPageListReq req) {
    String url = betterwoodConfig.getBetterwoodPlatformUrl() +"/open-api/member/find/company/by_sales_id";

    Map<String, String> headers = new HashMap<>();
    headers.put("pageNo", req.getPageNo().toString());
    headers.put("pageSize", req.getPageSize().toString());
    try {
      JSONObject jsonObject = HttpClientUtil.doJsonPost(url, headers, req);
      if (jsonObject != null && jsonObject.getJSONObject("data") != null
          && StrUtil.isNotBlank(jsonObject.getJSONObject("data").getJSONArray("rows").toString())) {
        JSONObject data =  jsonObject.getJSONObject("data");
        List<CompanyPageListRes> records = JSONUtil.toList(data.getJSONArray("rows").toString(), CompanyPageListRes.class);
        Integer totalPage = data.getInteger("totalPage");
        Integer totalRows = data.getInteger("totalRows");
        PageInfo<CompanyPageListRes> pageRes = new PageInfo<>(records);
        pageRes.setTotal(totalRows);
        pageRes.setPages(totalPage);
        return pageRes;
      }
    } catch (Exception e) {
      log.error("百达屋查询错误 getCompanyListBySalesId", e);
    }
    return new PageInfo<>();
  }


  @ResultLog(name = "BdxRpcService.getCompanyListBySalesIdV2", methodType = MethodTypeEnum.HTTP_UP)
  public PageInfo<CompanyPageListRes> getCompanyListBySalesIdV2(CompanyPageListReq req) {
    Result<PageResult<CompanyPageListRes>> findCompanyFeignResult = companyFeignService.findCompanyBySalesId(req);

    if (findCompanyFeignResult == null || !findCompanyFeignResult.isOk()) {
      throw new RuntimeException("企业查询失败，请稍后再试");
    }
    PageResult<CompanyPageListRes> companyPage = findCompanyFeignResult.getData();
    companyPage.getRows().forEach(company -> {
      if (StrUtil.startWith(company.getTaxpayerId(), "1")) {
        company.setShowSMSChoice(true);
      }
      company.setContractStateLabel(CompanyContractEnum.getDesc(company.getContractState()));
      company.setUrl(sxeH5Url + COMPANY_CONTRACT_H5_PAGE +  company.getCompanyId());
    });

    PageInfo<CompanyPageListRes> pageRes = new PageInfo<>(companyPage.getRows());
    pageRes.setTotal(companyPage.getTotalRows());
    pageRes.setPages(companyPage.getTotalPage());
    return pageRes;
  }

  @ResultLog(name = "BdxRpcService.getCompanyMemberList", methodType = MethodTypeEnum.HTTP_UP)
  public PageInfo<CompanyMemberPageListRes> getCompanyMemberList(CompanyMemberPageListReq req) {
    String url = betterwoodConfig.getBetterwoodPlatformUrl() + "/open-api/member/find/member/by_company_id";
    Map<String, String> params = new HashMap<>();
    params.put("companyId", req.getCompanyId().toString());
    params.put("param", req.getParam());
    params.put("pageNo", req.getPageNo().toString());
    params.put("pageSize", req.getPageSize().toString());
    try {
      JSONObject jsonObject = HttpClientUtil.doGet(url, null, params);
      if (jsonObject != null && jsonObject.getJSONObject("data") != null
          && StrUtil.isNotBlank(jsonObject.getJSONObject("data").getJSONArray("rows").toString())) {
        JSONObject data =  jsonObject.getJSONObject("data");
        List<CompanyMemberPageListRes> records = JSONUtil.toList(data.getJSONArray("rows").toString(), CompanyMemberPageListRes.class);
        records.forEach(item -> {
              item.setMemberStatusLabel(MemberStatusEnum.fromCode(item.getMemberStatus()).getLabel());
              if (MemberStatusEnum.ACTIVATED.getCode().equals(item.getMemberStatus()) && item.getOutOrderFlag() == 1) {
                item.setOutOrderFlag(1);
                item.setMemberStatusLabel(MemberStatusEnum.ALLOW_OUT_ORDER.getLabel());
              } else {
                item.setOutOrderFlag(0);
              }
              item.setGuestPhoneAreaCode(item.getAreaCode());
        });
        Integer totalPage = data.getInteger("totalPage");
        Integer totalRows = data.getInteger("totalRows");
        PageInfo<CompanyMemberPageListRes> pageRes = new PageInfo<>(records);
        pageRes.setTotal(totalRows);
        pageRes.setPages(totalPage);
        return pageRes;
      }
    } catch (Exception e) {
      log.error("百达屋查询错误 getCompanyMemberList", e);
    }
    return new PageInfo<>();
  }

  @ResultLog(name = "BdxRpcService.getCompanyMemberListV2", methodType = MethodTypeEnum.HTTP_UP)
  public PageInfo<CompanyMemberPageListRes> getCompanyMemberListV2(CompanyMemberPageListReq req) {

    Result<PageResult<CompanyMemberPageListRes>> pageResult= companyFeignService.findMemberByCompanyId(req.getCompanyId(), req.getParam(), req.getPageNo(), req.getPageSize());
    if (pageResult == null || !pageResult.isOk() || pageResult.getData() == null) {
      return new PageInfo<>();
    }
    List<CompanyMemberPageListRes> records = pageResult.getData().getRows();
    records.forEach(record -> {
      record.setMemberStatusLabel(MemberStatusEnum.fromCode(record.getMemberStatus()).getLabel());
      if (MemberStatusEnum.ACTIVATED.getCode().equals(record.getMemberStatus()) && record.getOutOrderFlag() == 1) {
        record.setOutOrderFlag(1);
        record.setMemberStatusLabel(MemberStatusEnum.ALLOW_OUT_ORDER.getLabel());
      } else {
        record.setOutOrderFlag(0);
      }
      record.setGuestPhoneAreaCode(record.getAreaCode());
    });
    PageInfo<CompanyMemberPageListRes> pageRes = new PageInfo<>(records);
    pageRes.setTotal(pageResult.getData().getTotalRows());
    pageRes.setPages(pageResult.getData().getTotalPage());
    return pageRes;

  }


  @ResultLog(name = "BdxRpcService.salesmanQuery", methodType = MethodTypeEnum.HTTP_UP)
  public SalesmanInfoVO salesmanQuery(SalesmanQueryReq req) {
    String url = betterwoodConfig.getBetterwoodPlatformUrl() + "/open-api/sales/salesmanQuery";
    try {
      RpcHttpResponse response = HttpClientUtil.doPost(url, JSONObject.toJSONString(req));
      log.info("BdxRpcService.salesmanQuery,url:{},params:{},jsonObject:{}",url,JSONObject.toJSONString(req),response);
      if (ObjectUtil.isNull(response) || !response.isOk()) {
        return null;
      }
      JSONObject jsonObject = JSONObject.parseObject(response.getBody());
      if (jsonObject.get("data") != null) {
        return JSONUtil.toBean(jsonObject.get("data").toString(), SalesmanInfoVO.class);
      }
    } catch (Exception e) {
      log.error("百达屋查询错误 getCompanyMemberList", e);
    }
    return null;
  }

  @ResultLog(name = "BdxRpcService.getCompanyMemberList", methodType = MethodTypeEnum.HTTP_UP)
  public CompanyInfoVO findByCompanyId(Long companyId) {
    String url = betterwoodConfig.getBetterwoodPlatformUrl() + "/open-api/member/find/company/by_id";
    Map<String, String> params = new HashMap<>();
    params.put("companyId", companyId.toString());
    try {
      JSONObject jsonObject = HttpClientUtil.doGet(url, null, params);
      if (jsonObject.getJSONObject("data") != null) {
        JSONObject data =  jsonObject.getJSONObject("data");
        return data.toJavaObject(CompanyInfoVO.class);
      }
    } catch (Exception e) {
      log.error("百达屋查询错误 getCompanyMemberList", e);
    }
    return null;
  }

  @ResultLog(name = "BdxRpcService.addOrUpdateCompany", methodType = MethodTypeEnum.HTTP_UP)
  public CompanyVO addOrUpdateCompany(AddOrUpdateCompanyReq req) {
    String url = betterwoodConfig.getBetterwoodPlatformUrl() + "/open-api/member/company/member/addOrUpdateCompany";
    try {
      RpcHttpResponse response = HttpClientUtil.doPost(url, JSONObject.toJSONString(req));
      log.info("BdxRpcService.addOrUpdateCompany, url:{}, params:{}, jsonObject:{}", url, JSONObject.toJSONString(req), response);
      if (ObjectUtil.isNull(response) || !response.isOk()) {
        throw new BdwServiceException("百达屋请求失败 addOrUpdateCompany");
      }
      JSONObject jsonObject = JSONObject.parseObject(response.getBody());
      if (jsonObject.getJSONObject("data") != null) {
        JSONObject data = jsonObject.getJSONObject("data");
        return data.toJavaObject(CompanyVO.class);
      }
    } catch (Exception e) {
      log.error("百达屋请求失败 addOrUpdateCompany", e);
    }
    return null;
  }

  @ResultLog(name = "BdxRpcService.checkCompanyInfo", methodType = MethodTypeEnum.HTTP_UP)
  public String checkCompanyInfo(CompanyApplyReq req) {
    String url = betterwoodConfig.getBetterwoodPlatformUrl() + "/open-api/member/company/member/checkCompanyInfo";
    try {
      RpcHttpResponse response = HttpClientUtil.doPost(url, JSONObject.toJSONString(req));
      log.info("BdxRpcService.checkCompanyInfo, url:{}, params:{}, jsonObject:{}", url, JSONObject.toJSONString(req), response);
      if (ObjectUtil.isNull(response) || !response.isOk()) {
        throw new ServiceException(ResultCode.ERROR);
      }
      JSONObject jsonObject = JSONObject.parseObject(response.getBody());
      if (jsonObject != null && jsonObject.getInteger("code") == 0) {
        return jsonObject.getString("msg");
      } else {
        return null;
      }
    } catch (Exception e) {
      log.error("百达屋请求失败 addOrUpdateCompany", e);
    }
    return null;
  }


  @ResultLog(name = "BdxRpcService.companyProtocolAdd", methodType = MethodTypeEnum.HTTP_UP)
  public CompanyProtocol companyProtocolAdd(CompanyProtocolReq req) {
    String url = betterwoodConfig.getBetterwoodPlatformUrl() + "/open-api/member/company/member/protocol/add";
    Map<String, String> params = new HashMap<>();
    params.put("companyId", req.getCompanyId());
    params.put("hotelCode", req.getHotelCode());
    params.put("protocolNo", req.getProtocolNo());
    params.put("type", Integer.toString(req.getType()));
    try {
      JSONObject jsonObject = HttpClientUtil.doGet(url, null, params);
      log.info("BdxRpcService.companyProtocolAdd, url:{}, params:{}, jsonObject:{}", url, JSONObject.toJSONString(params), jsonObject);
      if (jsonObject.getJSONObject("data") != null) {
        JSONObject data = jsonObject.getJSONObject("data");
        return data.toJavaObject(CompanyProtocol.class);
      }
    } catch (Exception e) {
      log.error("百达屋请求失败 companyProtocolAdd", e);
    }
    return null;
  }

  @ResultLog(name = "BdxRpcService.companyProtocolDelete", methodType = MethodTypeEnum.HTTP_UP)
  public Boolean companyProtocolDelete(String id) {
    String url = betterwoodConfig.getBetterwoodPlatformUrl() + "/open-api/member/company/member/protocol/delete";
    Map<String, String> params = new HashMap<>();
    params.put("id", id);
    try {
      JSONObject jsonObject = HttpClientUtil.doGet(url, null, params);
      log.info("BdxRpcService.companyProtocolDelete, url:{}, params:{}, jsonObject:{}", url, JSONObject.toJSONString(params), jsonObject);
      if (jsonObject != null && jsonObject.getInteger("code") != null) {
        return jsonObject.getInteger("code") == 1;
      }
    } catch (Exception e) {
      log.error("百达屋请求失败 companyProtocolAdd", e);
    }
    return false;
  }


  @ResultLog(name = "BdxRpcService.queryHotelContract", methodType = MethodTypeEnum.HTTP_UP)
  public CompanyContractVO queryHotelContract(Long companyId, String hotelCode) {

    Result<CompanyContractVO> feignResult = companyFeignService.queryHotelContract(companyId, hotelCode);

    if (feignResult == null || !feignResult.isOk()) {
      throw new RuntimeException("企业合同查询失败，请稍后再试");
    }
    return feignResult.getData();
  }

  @ResultLog(name = "BdxRpcService.queryHotelRateCodeList", methodType = MethodTypeEnum.HTTP_UP)
  public List<PricePlanHotelVO> queryHotelRateCodeList(String hotelCode) {

    Result<PageVO<PricePlanHotelVO>> rateCodeListResult = roomPriceRemoteService.page(1, 999, null, "HOTEL", null, hotelCode,
            null, null, "PROTOCOL_UNIT", null, null, null);

    if (rateCodeListResult == null || !rateCodeListResult.isOk()) {
      throw new RuntimeException("房价码列表查询失败，请稍后再试");
    }
    return rateCodeListResult.getData().getRecords();
  }

  /**
   * 企业合同新增或更新
   * @param qo 合同信息
   * @return 合同信息
   */
  @ResultLog(name = "BdxRpcService.companyContractOrUpdate", methodType = MethodTypeEnum.HTTP_UP)
  public CompanyContractVO companyContractOrUpdate(CompanyContractQo qo) {

    Result<CompanyContractVO> feignResult = companyFeignService.companyContractOrUpdate(qo);

    if (feignResult == null || !feignResult.isOk()) {
      throw new RuntimeException("企业合同提交失败，请稍后再试");
    }
    return feignResult.getData();
  }
}
