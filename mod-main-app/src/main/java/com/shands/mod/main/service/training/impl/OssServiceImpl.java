package com.shands.mod.main.service.training.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.shands.mod.dao.mapper.syncuc.ModHotelInfoDao;
import com.shands.mod.dao.model.hs.enums.OtaChannelEnum;
import com.shands.mod.dao.model.req.syncuc.SendRoomPriceImgInfoReq;
import com.shands.mod.dao.model.res.syncuc.RoomPriceImgInfoRes;
import com.shands.mod.exception.ServiceException;
import com.shands.mod.file.FileStorageUtil;
import com.shands.mod.main.service.training.OssService;
import com.shands.mod.main.util.ThreadLocalHelper;
import com.shands.mod.util.BaseConstants;
import com.shands.mod.util.Tools;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @Date 2022/8/29
 **/
@Service
@Slf4j
public class OssServiceImpl implements OssService {

  @Value("${oss.main.otaImgPath}")
  private String otaImgPath;
  @Value("${oss.main.priceMonitorPath}")
  private String priceMonitorPath;


  private static final String businessLicensePath = "main/image/businessLicense";
  private static final String contractAttachmentPath = "main/image/contractAttachment";

  @Autowired
  private ModHotelInfoDao modHotelInfoDao;
  @Resource
  private RedisTemplate<String, Map<Integer, String>> redisTemplate;

  @Autowired
  private FileStorageUtil fileStorageUtil;

  @Autowired
  private Environment environment;


  @Override
  public String uploadFile(MultipartFile file, String fileName,String type) {
    long l = System.currentTimeMillis();
    log.info("上传视频请求_{}",l);
    if (file == null) {
      throw new ServiceException("请先上传文件");
    }
    String uploadOss;
    String objectName = null;
    //文件夹 (其他类型加type)
    if ("VIDEO".equals(type)){
      objectName = "main/video/";
    }else if ("PDF".equals(type)){
      objectName = "main/pdf/";
    }else if ("EXCEL".equals(type)){
      objectName = "main/excel/";
    }else {
      objectName = "main/image/";
    }
    try {
      uploadOss = this.uploadOss(file, fileName,objectName);

    } catch (Exception e) {
      log.error("上传文件异常 ", e);
      throw new ServiceException("上传失败，请重新上传");
    }
    log.info("上传视频耗时_{}",System.currentTimeMillis() - l);
    return uploadOss;
  }

  private String uploadOss(MultipartFile file, String fileName, String objectName) throws ServiceException {
    try {
      //文件类型
      InputStream inputStream = file.getInputStream();
      String suffix = file.getOriginalFilename()
          .substring(file.getOriginalFilename().lastIndexOf("."));
      // 文件加 名称加后缀
      fileName = fileName.replace("-", "");
      objectName = objectName + fileName + suffix;
      return fileStorageUtil.putInputStream(objectName, inputStream);
    } catch (Exception e) {
      log.error("图片上传oss失败，fileName {}", fileName, e);
      throw new ServiceException("上传失败，请重新上传");
    }
  }

//  /**
//   * 上传oss
//   * @param file
//   * @param fileName
//   * @param objectName 文件夹
//   * @return
//   */
//  private String uploadOss(MultipartFile file, String fileName,String objectName) {
//    OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
//
//    try {
//      //文件类型
//      InputStream inputStream = file.getInputStream();
//      String suffix = file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf("."));
//      // 文件加 名称加后缀
//      fileName = fileName.replace("-","");
//      objectName = objectName + fileName + suffix;
//      // 创建PutObject请求。
//      ossClient.putObject(bucketName, objectName, inputStream);
//    } catch (IOException e) {
//      log.error("图片上传oss失败，fileName " + fileName, e);
//      throw new com.aliyun.oss.ServiceException("图片上传至oss失败",e);
//    } finally {
//      if (ossClient != null) {
//        ossClient.shutdown();
//      }
//    }
//    return "https://" + bucketName + "." + endpoint + "/" + objectName;
//  }

  @Override
  public void verifyUpload(Map<Integer, String> files) {

    if (!isInTime4Upload()){
      throw new RuntimeException("请在9:00-14:00时间段内上传");
    }
    String hotelCode = modHotelInfoDao.queryById(ThreadLocalHelper.getCompanyId()).getHotelCode();
    String uploadDate = DateFormatUtils.format(System.currentTimeMillis(),
        BaseConstants.FORMAT_DATE);

    //限制：单酒店每天在配置时间段9:00-14:00内只允许上传一次
    String hotelKey = StrUtil.join(":",hotelCode, uploadDate);
    String hotelUploadImgKey = Tools.buildKey(BaseConstants.CACHE_PRICE_MONITOR, hotelKey);
    if (Boolean.TRUE.equals(redisTemplate.hasKey(hotelUploadImgKey))) {
      throw new ServiceException("该酒店今天已上传过,请勿重复上传");
    }

    Map<String, String> urlMap = files.entrySet()
        .stream()
        .collect(
            Collectors.toMap(e -> OtaChannelEnum.getByCode(e.getKey()).getName(), Entry::getValue));

    SendRoomPriceImgInfoReq imgInfoReq = SendRoomPriceImgInfoReq.builder()
        .arrivalDate(uploadDate)
        .hotelCode(hotelCode)
        .data(urlMap)
        .build();

    log.info("通知价格监控服务....请求参数={}",imgInfoReq.toString());
    try {
      String res = HttpUtil.post(priceMonitorPath, JSON.toJSONString(imgInfoReq));
      RoomPriceImgInfoRes imgInfoRes = JSON.parseObject(res, RoomPriceImgInfoRes.class);
      if (imgInfoRes.getCode() != 1){
        log.error("[价格监控服务][通知失败],响应:{}",imgInfoRes.toString());
        throw new ServiceException("价格监控服务通知失败!",imgInfoRes.toString());
      }
    }catch (Exception e){
      log.error("[价格监控服务][通知失败]",e.getMessage(),e);
      throw new ServiceException("价格监控服务通知失败!",e.getMessage());
    }
    //设置入redis，使key在当天结束时失效
    redisTemplate.opsForValue().set(hotelUploadImgKey,files,getSecondsUntilEndOfDay(), TimeUnit.SECONDS);
  }

  @Override
  public String uploadImage2Oss(MultipartFile file,Integer ota){
    // uploadPath: monitor/test
    //上传路径：测试环境 monitor/test/日期/酒店；线上环境 monitor/prod/日期/酒店
    //图片名称：渠道_员工_uuid
    if (!isInTime4Upload()){
      throw new RuntimeException("请在9:00-14:00时间段内上传");
    }
    try {
      String hotelCode = modHotelInfoDao.queryById(ThreadLocalHelper.getCompanyId()).getHotelCode();
      Integer userId = Optional.ofNullable(ThreadLocalHelper.getUser().getId())
          .orElse(-1);
      String uploadDate = DateFormatUtils.format(System.currentTimeMillis(),
          BaseConstants.FORMAT_DATE);

      // 处理每个OTA渠道上传的图片
      OtaChannelEnum channelEnum = OtaChannelEnum.getByCode(ota);
      //图片上传文件夹
      String path = StrUtil.join("/", otaImgPath, uploadDate, hotelCode) + "/";
      //图片的文件名
      String filename =
          StrUtil.join("_", channelEnum.getOssPath(), userId, Tools.createUUID(false));
      // 文件上传
      log.info("[图片上传oss][filename={},path={}]",filename,path);
      return this.uploadOss(file, filename, path);
    } catch (Exception e) {
      throw new com.aliyun.oss.ServiceException("图片上传至oss失败", e);
    }
  }

  @Override
  public String uploadBusinessLicense(MultipartFile file) {
    try {
      String uploadDate = DateFormatUtils.format(System.currentTimeMillis(), BaseConstants.FORMAT_DATE);

      //图片上传文件夹
      String path = StrUtil.join("/", businessLicensePath, uploadDate, "");
      //图片的文件名
      String filename = StrUtil.join("_", Tools.createUUID(false));
      // 文件上传
      log.info("营业执照上传 filename:{}, path:{}", filename, path);
      String imageUrl = this.uploadOss(file, filename, path);
      // 如果是测试或开发环境
      if (ArrayUtils.contains(BaseConstants.ENV_TEST_LIST, environment.getActiveProfiles()[0])) {
        return imageUrl.replace("bdw-test.oss-cn-shenzhen.aliyuncs.com", "test.img.betterwood.com");
      } else {
        return imageUrl.replace("oss-cn-shenzhen.aliyuncs.com", "betterwood.com");
      }

    } catch (Exception e) {
      throw new com.aliyun.oss.ServiceException("图片上传失败", e);
    }

  }

  @Override
  public String uploadContractAttachment(MultipartFile file) {
    try {
      String uploadDate = DateFormatUtils.format(System.currentTimeMillis(), BaseConstants.FORMAT_DATE);

      //图片上传文件夹
      String path = StrUtil.join("/", contractAttachmentPath, uploadDate, "");
      //图片的文件名
      String filename = StrUtil.join("_", Tools.createUUID(false));
      // 文件上传
      log.info("合同附件上传 filename:{}, path:{}", filename, path);
      String imageUrl = this.uploadOss(file, filename, path);
      // 如果是测试或开发环境
      if (ArrayUtils.contains(BaseConstants.ENV_TEST_LIST, environment.getActiveProfiles()[0])) {
        return imageUrl.replace("bdw-test.oss-cn-shenzhen.aliyuncs.com", "test.img.betterwood.com");
      } else {
        return imageUrl.replace("oss-cn-shenzhen.aliyuncs.com", "betterwood.com");
      }

    } catch (Exception e) {
      throw new com.aliyun.oss.ServiceException("合同附件上传失败", e);
    }

  }

  @Override
  public Map<String, String> dailyUploadedImgMap(){
    String hotelCode = modHotelInfoDao.queryById(ThreadLocalHelper.getCompanyId()).getHotelCode();
    String uploadDate = DateFormatUtils.format(System.currentTimeMillis(),
        BaseConstants.FORMAT_DATE);
    log.info("[显示酒店当天上传的各渠道图片][hotelCode={},uploadDate={}]",hotelCode,uploadDate);
    String hotelKey = StrUtil.join(":",hotelCode, uploadDate);
    String hotelUploadImgKey = Tools.buildKey(BaseConstants.CACHE_PRICE_MONITOR, hotelKey);

    Map<Integer, String> urlMap = redisTemplate.opsForValue().get(hotelUploadImgKey);
    if (CollectionUtil.isEmpty(urlMap)){
      throw new ServiceException("该酒店今天未上传图片");
    }
    return urlMap.entrySet()
        .stream()
        .collect(Collectors.toMap(e -> String.valueOf(e.getKey()), Entry::getValue));
  }

  /**
   * 计算当前时间距离当天23:59:59的秒数
   */
  private static int getSecondsUntilEndOfDay() {
    LocalDateTime now = LocalDateTime.now();
    LocalDateTime endOfDay = LocalDate.from(now).plusDays(1).atStartOfDay().minusSeconds(1);
    return (int) (endOfDay.toEpochSecond(ZoneOffset.UTC) - now.toEpochSecond(ZoneOffset.UTC));
  }

  /**
   * 判断当前时间是否在9:00～14:00之间，不在,则不允许上传
   */
  private static boolean isInTime4Upload() {
    LocalTime currentTime = LocalTime.now();

    LocalTime startTime = LocalTime.of(9, 0);
    LocalTime endTime = LocalTime.of(14, 0);

    return currentTime.isAfter(startTime) && currentTime.isBefore(endTime);
  }
}
