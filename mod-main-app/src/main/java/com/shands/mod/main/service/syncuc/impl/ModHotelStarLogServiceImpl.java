package com.shands.mod.main.service.syncuc.impl;

import com.shands.mod.dao.mapper.syncuc.ModHotelStarLogDao;
import com.shands.mod.dao.model.syncuc.ModHotelStarLog;
import com.shands.mod.main.service.syncuc.IModHotelStarLogService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * (ModHotelStarLog)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-11-01 19:57:45
 */
@Service("modHotelStarLogService")
public class ModHotelStarLogServiceImpl implements IModHotelStarLogService {

    @Resource
    private ModHotelStarLogDao modHotelStarLogDao;

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    @Override
    public ModHotelStarLog queryById(Integer id) {
        return this.modHotelStarLogDao.queryById(id);
    }

    /**
     * 查询多条数据
     *
     * @param offset 查询起始位置
     * @param limit 查询条数
     * @return 对象列表
     */
    @Override
    public List<ModHotelStarLog> queryAllByLimit(int offset, int limit) {
        return this.modHotelStarLogDao.queryAllByLimit(offset, limit);
    }

    /**
     * 新增数据
     *
     * @param modHotelStarLog 实例对象
     * @return 实例对象
     */
    @Override
    public ModHotelStarLog insert(ModHotelStarLog modHotelStarLog) {
        this.modHotelStarLogDao.insert(modHotelStarLog);
        return modHotelStarLog;
    }

    /**
     * 修改数据
     *
     * @param modHotelStarLog 实例对象
     * @return 实例对象
     */
    @Override
    public ModHotelStarLog update(ModHotelStarLog modHotelStarLog) {
        this.modHotelStarLogDao.update(modHotelStarLog);
        return this.queryById(modHotelStarLog.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    @Override
    public boolean deleteById(Integer id) {
        return this.modHotelStarLogDao.deleteById(id) > 0;
    }
}