package com.shands.mod.main.service.crm.impl;

import cn.hutool.core.collection.CollUtil;
import com.shands.mod.dao.mapper.outsource.OutUserInfoDao;
import com.shands.mod.dao.mapper.syncuc.ModDeptDao;
import com.shands.mod.dao.mapper.syncuc.ModHotelInfoDao;
import com.shands.mod.dao.mapper.syncuc.ModRoleUcDao;
import com.shands.mod.dao.mapper.syncuc.ModUserDao;
import com.shands.mod.dao.model.ModMenu;
import com.shands.mod.dao.model.syncuc.ModDept;
import com.shands.mod.dao.model.syncuc.ModHotelInfo;
import com.shands.mod.dao.model.syncuc.ModUser;
import com.shands.mod.dao.model.v0701.dto.UcLoginDto;
import com.shands.mod.dao.model.res.UcUserInfoByMobileVo;
import com.shands.mod.external.model.dto.UcAuthenticationDto;
import com.shands.mod.external.service.UcAuthenticationService;
import com.shands.mod.main.service.app.UcUserService;
import com.shands.mod.main.service.crm.UcLoginService;
import com.shands.mod.main.service.syncuc.IModUserTokenService;
import com.shands.mod.vo.UserInfoVO;
import com.shands.mod.util.BaseConstants;
import com.shands.mod.util.CommonConstants;
import com.shands.mod.util.JwtUtils;
import com.shands.mod.util.Tools;
import com.shands.uc.base.util.JwtUtil;
import com.shands.uc.model.req.v3.PublicLoginReq;
import com.shands.uc.model.req.v3.auth.RoleInfo;
import com.shands.uc.model.req.v3.auth.UserAuthInfo;
import com.shands.uc.model.req.issue.IssueUserQueryReq;
import com.shands.uc.model.res.issue.IssueUserResponse;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 2021/6/8
 * @desc 通宝登录service
*/
@Service
@Slf4j
public class UcLoginServiceImpl implements UcLoginService {

  @Value("${app.token.secret:shands-mod3-secret}")
  private String secret;

  @Value("${app.token.expiration:259200}")
  private long expiration;

  @Value("${uc.sercret:380583f66a4c462ba62ca690c231be9f}")
  private String ucSercret;

  @Value("${uc.appId:mod3}")
  private String ucAppId;

  @Value("${uc.domain:http://test-uc3.kaiyuanhotels.com}")
  private String domain;

  @Value("${sks.url:http://test.server.rest.sks.com}")
  private String sksUrl;

  private final UcAuthenticationService ucAuthenticationService;

  private final IModUserTokenService modUserTokenService;

  private final RedisTemplate redisTemplate;

  @Resource
  private ModHotelInfoDao modHotelInfoDao;

  @Resource
  private OutUserInfoDao outUserInfoDao;

  @Resource
  private ModUserDao modUserDao;

  @Resource
  private ModDeptDao modDeptDao;

  @Resource
  private ModRoleUcDao modRoleUcDao;
  @Resource
  private UcUserService ucUserService;

  public UcLoginServiceImpl(
      UcAuthenticationService ucAuthenticationService,
      IModUserTokenService modUserTokenService,
      RedisTemplate redisTemplate) {
    this.ucAuthenticationService = ucAuthenticationService;
    this.modUserTokenService = modUserTokenService;
    this.redisTemplate = redisTemplate;
  }

  @Override
  public UserInfoVO ucLogin(UcLoginDto ucLoginDto) {
    compatibleUcToken(ucLoginDto);
    String userId = JwtUtil.getUserIdFromToken(ucLoginDto.getUcToken(), JwtUtil.JWT_SECRET);
    if(StringUtils.isBlank(userId) || !StringUtils.isNumeric(userId)){
      throw new RuntimeException("无效uc用户token");
    }

    //清空用户权限缓存信息
    redisTemplate.delete(Tools.buildKey(CommonConstants.USER_UC_INFO, userId));
    redisTemplate.delete(Tools.buildKey(CommonConstants.USER_UC_ROLE, userId));
    redisTemplate.delete(Tools.buildKey(CommonConstants.USER_MOD_INFO, userId));

    //通宝调用基础请求参数
    UcAuthenticationDto ucAuthenticationDto = new UcAuthenticationDto();
    ucAuthenticationDto.setAppId(ucAppId);
    ucAuthenticationDto.setSecret(ucSercret);
    ucAuthenticationDto.setDomain(domain);
    ucAuthenticationDto.setToken(ucLoginDto.getUcToken());

    PublicLoginReq findUserInfoByMobileReq = new PublicLoginReq();
    findUserInfoByMobileReq.setAppId(ucAppId);

    UserAuthInfo userAuthInfo = ucAuthenticationService.loginByToken(ucAuthenticationDto, findUserInfoByMobileReq);
    if(userAuthInfo == null){
      throw new RuntimeException("uc用户信息获取为空");
    }

    //解析通宝用户信息
    if(userAuthInfo.getRoles() == null || userAuthInfo.getRoles().isEmpty()){
      throw new RuntimeException("uc用户角色为空");
    }

    //更新酒店微管家用户信息
    updUcUserInfo(userAuthInfo);

    if(!userAuthInfo.isStatus()){
      throw new RuntimeException("该人员处于离职状态");
    }

    //生成酒店微管家用户token
    ModUser user = modUserDao.queryByUcid(userAuthInfo.getUserId());
    if(user == null){
      throw new RuntimeException("用户信息为空");
    }

    //查询用户所属机构信息
    ModHotelInfo modHotelInfo = modHotelInfoDao.queryByUcCompanyId(userAuthInfo.getCompanyId());
    if(modHotelInfo == null || modHotelInfo.getHotelStatus() == 0){
      throw new RuntimeException("未查询到您的机构酒店信息");
    }

    // 创建token
    String token = JwtUtils.generateToken(user.getId().toString(), this.secret, this.expiration);
    if(StringUtils.isBlank(token)){
      throw new RuntimeException("系统token生成失败");
    }

    UserInfoVO result = new UserInfoVO();
    BeanUtils.copyProperties(user,result);
    result.setToken(token);
    result.setUcUser(true);
    result.setHotelId(modHotelInfo.getHotelId());
    result.setHotelCode(modHotelInfo.getHotelCode());
    result.setHotelName(modHotelInfo.getHotelName());

    //初始化用户部门信息
    Integer ucDeptId = userAuthInfo.getDeptId();
    if(ucDeptId != null){

      //根据通宝部门id查询部门信息
      ModDept modDept = modDeptDao.queryByUcDeptId(ucDeptId);
      if(modDept != null){
        result.setDeptId(modDept.getId());
        result.setUcDeptId(ucDeptId);
        result.setDeptName(modDept.getName());
      }
    }

    //设置用户角色权限信息
    getRights(result,userAuthInfo,null);

    //标识登录入口
    result.setPlatform("PC");

    String key = Tools.buildKey(BaseConstants.CACHE_USER, token);
    this.redisTemplate.opsForValue().set(key, result, this.expiration, TimeUnit.SECONDS);

    //记录用户token信息
    modUserTokenService.saveTokenInfo(user.getId(),token,expiration,"PC");
    return result;
  }

  @Override
  public UserInfoVO ucLoginById(Integer ucId) {
    ModUser modUser = modUserDao.queryByUcid(ucId);
    if(null == modUser) {
      throw new RuntimeException("用户信息为空");
    }
    String token = ucUserService.loginByMobileAndPassword(modUser.getMobile(), modUser.getPassword());

    UcLoginDto ucLoginDto = new UcLoginDto();
    ucLoginDto.setUcToken(token);
    return this.ucLogin(ucLoginDto);
  }

  @Override
  public void getRights(UserInfoVO result,UserAuthInfo userAuthInfo,String plantform) {

    Integer ucCompanyId = userAuthInfo.getCompanyId();
    if(ucCompanyId == null){
      return;
    }

    List<String> keys = userAuthInfo.getRoles().stream().
        map(RoleInfo :: getCode).collect(Collectors.toList());

    result.setRoles(keys);

    //查询权限列表
    List<String> perms = new ArrayList<>();
    //后台管理菜单权限
    List<String> permsPlat = new ArrayList<>();
    if (plantform != null && !"".equals(plantform)){
      perms = modRoleUcDao.selectPcPerms(keys,plantform);
    }else {

      List<ModMenu> modMenus = modRoleUcDao.selectAllPerms(keys);
      if (!modMenus.isEmpty()){
        Map<String, List<ModMenu>> plantCodeMap = modMenus.stream()
            .collect(Collectors.groupingBy(ModMenu::getPlantformCode));

        List<ModMenu> pc = plantCodeMap.get("PC");
        if (pc != null && !pc.isEmpty()){
          perms = pc.stream().map(ModMenu::getPerms).collect(Collectors.toList());
        }

        List<ModMenu> plat = plantCodeMap.get("PLAT");
        if (plat != null && !plat.isEmpty()){
          permsPlat = plat.stream().map(ModMenu::getPerms).collect(Collectors.toList());
        }
      }

    }

    if(perms.isEmpty() && permsPlat.isEmpty()){
      throw new RuntimeException("用户权限不足");
    }

    //如果是admin或者IT维护人员，查询所有微管家已上线酒店列表信息
    ModHotelInfo hotelInfo = modHotelInfoDao.queryByUcCompanyId(ucCompanyId);

    if(hotelInfo == null){
      throw new RuntimeException("机构或酒店信息未查询到");
    }

    result.setCompany(hotelInfo.getHotelId());
    result.setPermissions(perms);
    result.setPermissionsPlat(permsPlat);
  }

  /**
   * 同步人员通宝信息
   * @param userAuthInfo
   * @return
   */
  @Override
  @Transactional(propagation = Propagation.REQUIRES_NEW)
  public boolean updUcUserInfo(UserAuthInfo userAuthInfo){
    
    ModUser updUser = new ModUser();
    updUser.setName(userAuthInfo.getName());
    updUser.setUsername(userAuthInfo.getMobile());
    updUser.setMobile(userAuthInfo.getMobile());
    updUser.setEmail(userAuthInfo.getEmail());
    updUser.setSource("uc");
    updUser.setUcId(userAuthInfo.getUserId());
    updUser.setIdCard(userAuthInfo.getIdCard());
    updUser.setDeptId(userAuthInfo.getDeptId());
    updUser.setUcCompanyId(userAuthInfo.getCompanyId());
    updUser.setPostId(userAuthInfo.getPostId());
    updUser.setUpdateTime(new Date());

    if(!userAuthInfo.isStatus()){
      updUser.setDeleted(1);
    }else{
      updUser.setDeleted(0);
    }

    //如果人员信息存在多条，就先把外包人员记录变成离职状态
    ModUser qurObj = modUserDao.queryByMobileAndUcCompanyId(userAuthInfo.getMobile(),userAuthInfo.getCompanyId());
    if(qurObj != null){
      modUserDao.updateById(qurObj.getId());
      //外包人员信息表离职操作
      outUserInfoDao.updateByUserId(qurObj.getId());
    }

    int rec = modUserDao.updateByUcId(updUser);

    log.info("[通宝人员信息同步][数据影响行数：{}]",rec);

    return rec > 0;
  }

  /**
   * 兼容德胧管理后台ucToken
   */
  private void compatibleUcToken(UcLoginDto ucLoginDto){
    if(null != ucLoginDto && StringUtils.isNotBlank(ucLoginDto.getUcToken())) {
      ucLoginDto.setUcToken(org.apache.commons.lang3.StringUtils.remove(ucLoginDto.getUcToken(), "Bearer "));
    }
  }

  @Override
  public UcUserInfoByMobileVo queryByMobile(String mobile) {

    // 构建通宝认证信息
    UcAuthenticationDto ucAuthenticationDto = new UcAuthenticationDto();
    ucAuthenticationDto.setAppId(ucAppId);
    ucAuthenticationDto.setSecret(ucSercret);
    ucAuthenticationDto.setDomain(domain);

    // 构建用户查询请求
    IssueUserQueryReq issueUserQueryReq = new IssueUserQueryReq();
    issueUserQueryReq.setMobile(mobile);
    issueUserQueryReq.setStatus(1);
    issueUserQueryReq.setDeleted(0);

    // 调用通宝接口查询用户信息
    List<IssueUserResponse> issueUserResponses = ucAuthenticationService.syncUser(ucAuthenticationDto, issueUserQueryReq);

    if (CollUtil.isEmpty(issueUserResponses)) {
      log.warn("通过手机号查询通宝用户信息为空，手机号：{}", mobile);
      throw new RuntimeException("未查询到用户信息");
    }

    // 取第一个用户信息
    IssueUserResponse userResponse = issueUserResponses.get(0);

    // 构建返回对象
    UcUserInfoByMobileVo result = new UcUserInfoByMobileVo();
    result.setSalesId(userResponse.getId());
    result.setSalesman(userResponse.getName());
    result.setSalesmanPhone(userResponse.getMobile());

    log.info("通过手机号查询通宝用户信息成功，手机号：{}，用户ID：{}，用户名：{}", mobile, userResponse.getId(), userResponse.getName());

    return result;

  }
}
