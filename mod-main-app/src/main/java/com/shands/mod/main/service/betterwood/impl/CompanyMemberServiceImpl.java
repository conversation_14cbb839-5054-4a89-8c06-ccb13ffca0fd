package com.shands.mod.main.service.betterwood.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.betterwood.base.common.model.Result;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.shands.mod.dao.mapper.sales.tool.ModCompanyApplyRecordMapper;
import com.shands.mod.dao.model.rolepermissionnew.PermissionNew;
import com.shands.mod.dao.model.sales.tool.domain.ModCompanyApplyRecord;
import com.shands.mod.dao.model.sales.tool.req.AddOrUpdateCompanyReq;
import com.shands.mod.dao.model.sales.tool.req.CompanyApplyReq;
import com.shands.mod.dao.model.sales.tool.req.CompanyContractAddOrUpdateReq;
import com.shands.mod.dao.model.sales.tool.req.CompanyContractQo;
import com.shands.mod.dao.model.sales.tool.req.CompanyMemberPageListReq;
import com.shands.mod.dao.model.sales.tool.req.CompanyPageListReq;
import com.shands.mod.dao.model.sales.tool.req.GetOrSaveEnterpriseReq;
import com.shands.mod.dao.model.sales.tool.res.*;
import com.shands.mod.dao.model.syncuc.ModHotelInfo;
import com.shands.mod.exception.ServiceException;
import com.shands.mod.external.model.dto.UcAuthenticationDto;
import com.shands.mod.external.service.UcAuthenticationService;
import com.shands.mod.main.config.CompanyMemberConfig;
import com.shands.mod.main.config.UcAppConfig;
import com.shands.mod.main.remote.member.CompanyMemberFeignService;
import com.shands.mod.main.service.betterwood.CompanyMemberService;
import com.shands.mod.main.service.betterwood.rpc.BdxRpcService;
import com.shands.mod.main.service.betterwood.rpc.resp.CompanyContractVO;
import com.shands.mod.main.service.betterwood.rpc.resp.CompanyVO;
import com.shands.mod.main.service.betterwood.rpc.resp.PricePlanHotelVO;
import com.shands.mod.main.service.businessLicense.BizLicenseOCRService;
import com.shands.mod.main.service.pms.IPmsService;
import com.shands.mod.main.service.rolepermissionnew.PermissionNewService;
import com.shands.mod.main.service.syncuc.IModHotelInfoService;
import com.shands.mod.main.service.training.OssService;
import com.shands.mod.main.util.CompanyInfoCheckUtil;
import com.shands.mod.main.util.ThreadLocalHelper;
import com.shands.mod.message.util.RedisUtils;
import com.shands.mod.util.BaseConstants;
import com.shands.mod.util.BaseThreadLocalHelper;
import com.shands.uc.model.req.issue.IssueUserQueryReq;
import com.shands.uc.model.res.issue.IssueUserResponse;
import com.tencentcloudapi.ocr.v20181119.models.EnterpriseLicenseInfo;
import com.tencentcloudapi.ocr.v20181119.models.EnterpriseLicenseOCRResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CompanyMemberServiceImpl implements CompanyMemberService {

  @Autowired
  private BdxRpcService bdxRpcService;

  @Autowired
  private IModHotelInfoService hotelInfoService;

  @Resource
  private PermissionNewService permissionNewService;

  @Autowired
  private IPmsService pmsService;

  @Autowired
  private OssService ossService;

  @Autowired
  private BizLicenseOCRService bizLicenseOcrService;

  @Autowired
  private CompanyMemberConfig companyMemberConfig;

  @Autowired
  private RedisUtils redisUtils;

  @Resource
  private UcAuthenticationService ucAuthenticationService;

  @Autowired
  private UcAppConfig ucAppConfig;

  @Resource
  private ModCompanyApplyRecordMapper modCompanyApplyRecordMapper;

  @Autowired
  private CompanyMemberFeignService companyMemberFeignService;


  private static final String ALL_COMPANY_PERMS= "sales_assistant_all";

  private final int oneDaySeconds = 60 * 60 * 24;

  private static final String[] illegalCompanyNames = {"旅行社", "旅游公司", "旅游运营", "旅游服务"};


  @Override
  public PageInfo<CompanyPageListRes> queryCompanyPageList(CompanyPageListReq req) {
    // 查询酒店code
    ModHotelInfo hotelInfo = hotelInfoService.queryById(BaseThreadLocalHelper.getCompanyId());
    if (hotelInfo == null) {
      return new PageInfo<>();
    }
    req.setHotelCode(hotelInfo.getHotelCode());

    // 判断当前用户是否拥有查看酒店纬度所有企业的权限
    List<PermissionNew> getPermissionByUserId = permissionNewService.getPermissionByUserId(ThreadLocalHelper.getUser().getId());
    boolean hasPermission = getPermissionByUserId.stream()
        .anyMatch(p -> ALL_COMPANY_PERMS.equals(p.getPermissionCode()));
    if (hasPermission){
      // 原逻辑
      // 调用bdw的获取企业接口
      return bdxRpcService.getCompanyListByHotelCode(req);
    } else {
      Integer salesId = ThreadLocalHelper.getUser().getUcId();
      req.setSalesId(salesId);

      if (!companyMemberConfig.getRefactorCompanySwitch()) {
        // 调用pms获取销售员签约协议
        List<String> protocolNoList = pmsService.getProtocolNoBySaleManId(salesId);
        req.setProtocolNoList(protocolNoList);

      }
      return bdxRpcService.getCompanyListBySalesIdV2(req);

    }
  }

  @Override
  public PageInfo<CompanyMemberPageListRes> queryMemberPageList(CompanyMemberPageListReq req) {
    return bdxRpcService.getCompanyMemberListV2(req);
  }

  @Override
  public UploadBizLicenseRes bizLicenseUpload(MultipartFile file, Integer uploadType) {
    // 校验文件
    checkFile(file);
    log.info("上传营业执照 size:{} fileName:{} originalFileName:{} contentType: {}",
        file.getSize(), file.getName(), file.getOriginalFilename(), file.getContentType());
    // 上传
    String imageUrl = ossService.uploadBusinessLicense(file);
    if (StrUtil.isBlank(imageUrl)) {
      throw new ServiceException("上传失败");
    }
    UploadBizLicenseRes res  = new UploadBizLicenseRes();
    res.setBizLicenseImg(imageUrl);

    // 企查查直接返回
    if (uploadType == 1) {
      return res;
    }

    // OCR识别
    EnterpriseLicenseOCRResponse ocrResponse = bizLicenseOcrService.doEnterpriseImgOCR(imageUrl);

    if (ocrResponse == null || ocrResponse.getEnterpriseLicenseInfos().length <= 0) {
      throw new ServiceException("该营业执照无法识别，请上传正确的清晰证照。");
    }

    for (EnterpriseLicenseInfo enterpriseLicenseInfo : ocrResponse.getEnterpriseLicenseInfos()) {
      if (StrUtil.equals(enterpriseLicenseInfo.getName(), "统一社会信用代码") || StrUtil.equals(enterpriseLicenseInfo.getName(), "注册号")) {
        res.setTaxpayerId(enterpriseLicenseInfo.getValue());
      }

      if (StrUtil.isBlank(res.getCompanyName()) && StrUtil.contains(enterpriseLicenseInfo.getName(), "名称") ) {
        res.setCompanyName(enterpriseLicenseInfo.getValue());
      }

    }

    if ( StrUtil.isBlank(res.getTaxpayerId()) ) {
      throw new ServiceException("该营业执照无法识别，请上传正确的清晰证照。");
    }

    UploadBizLicenseRes copy  = new UploadBizLicenseRes();
    copy.setBizLicenseImg(res.getBizLicenseImg());
    copy.setTaxpayerId(res.getTaxpayerId());

    redisUtils.set(imageUrl, JSON.toJSONString(copy), oneDaySeconds * 3);
    return res;
  }

  @Override
  public PublicEmailRes getPublicEmailSuffix() {
    PublicEmailRes res = new PublicEmailRes();
    String publicEmailSuffix = companyMemberConfig.getPublicEmailSuffix();
    List<String> suffixList = Arrays.stream(publicEmailSuffix.split(",")).collect(Collectors.toList());
    res.setEmailSuffixList(suffixList);
    return res;
  }

  @Override
  public CompanyVO companyApply(CompanyApplyReq req) {

    // 处理请求参数
    handleCompanyApplyReq(req);

    if (req.getUploadType() == 0) {
      // 获取上传的营业执照OCR识别结果，比对
      String ocrRes = redisUtils.getString(req.getBizLicenseImg());
      if (StrUtil.isBlank(ocrRes)) {
        throw new ServiceException("请重新上传企业证照");
      }
      String companyName = req.getCompanyName();
      UploadBizLicenseRes uploadBizLicenseRes = JSON.parseObject(ocrRes, UploadBizLicenseRes.class);
      if (!StrUtil.equals(req.getTaxpayerId(), uploadBizLicenseRes.getTaxpayerId())  ) {
        throw new ServiceException("企业证照信息不匹配");
      }
    } else if (req.getUploadType() == 1 && (!req.getTaxpayerId().startsWith("1") || req.getTaxpayerId().length() != 18) ) {
      throw new ServiceException("请输入 1 开头的18位信用代码");
    }


    // 校验企业联系人手机号
    String checkMobileRes = bdxRpcService.checkCompanyInfo(req);
    if (checkMobileRes != null) {
      throw new ServiceException(checkMobileRes);
    }

    String salesId = String.valueOf(BaseThreadLocalHelper.getUser().getUcId());
    // 查询申请人所选酒店
    ModHotelInfo hotelInfo = hotelInfoService.queryById(BaseThreadLocalHelper.getCompanyId());
    String hotelCode = hotelInfo.getHotelCode();

    //调用通宝获取用户接口
    UcAuthenticationDto ucAuthenticationDto = new UcAuthenticationDto();
    ucAuthenticationDto.setAppId(ucAppConfig.getUcAppId());
    ucAuthenticationDto.setSecret(ucAppConfig.getUcSercret());
    ucAuthenticationDto.setDomain(ucAppConfig.getDomain());
    //调用通宝获取用户接口
    IssueUserQueryReq issueUserQueryReq = new IssueUserQueryReq();
    //通宝id
    issueUserQueryReq.setId(BaseThreadLocalHelper.getUser().getUcId());
    List<IssueUserResponse> issueUserResponses =
        ucAuthenticationService.syncUser(ucAuthenticationDto, issueUserQueryReq);
    IssueUserResponse saleManInfo = issueUserResponses.get(0);
    if (StrUtil.equals("GROUP", saleManInfo.getUserType())) {
        // 集团人员发展酒店code一律为000001
        hotelCode = BaseConstants.GROUP_CODE;
    }


    ModCompanyApplyRecord modCompanyApplyRecord = new ModCompanyApplyRecord();
    BeanUtil.copyProperties(req, modCompanyApplyRecord);
    modCompanyApplyRecord.setUcId(Long.valueOf(salesId));
    modCompanyApplyRecord.setHotelCode(hotelCode);
    // 校验营业执照格式
    if (!CompanyInfoCheckUtil.checkTaxpayerId(req.getTaxpayerId())) {
      applyCompanyFail(modCompanyApplyRecord, "统一社会信用代码不合规。");
    }

    // 校验企业名称格式
    if (!CompanyInfoCheckUtil.checkCompanyName(req.getCompanyName())) {
      throw new ServiceException("企业名称仅支持中文、大小写英文、数字、括号、中划线");
    }

    // 酒店人员校验企业名称
    if (!StrUtil.equals(hotelCode, BaseConstants.GROUP_CODE)) {
      if (!checkCompanyName(req.getCompanyName())) {
        applyCompanyFail(modCompanyApplyRecord, "旅行社类型企业暂不支持申请。");
      }
    }

    // 校验企业邮箱后缀
    if (StrUtil.isNotBlank(req.getEmailSuffix()) && getPublicEmailSuffix().getEmailSuffixList().contains(req.getEmailSuffix().toLowerCase())) {
        req.setEmailSuffix(null);
    }
    // bdw创建企业或修改企业信息
    AddOrUpdateCompanyReq addOrUpdateCompanyReq = new AddOrUpdateCompanyReq();
    BeanUtil.copyProperties(req, addOrUpdateCompanyReq);
    addOrUpdateCompanyReq.setSalesId(salesId);
    addOrUpdateCompanyReq.setHotelCode(hotelCode);
    addOrUpdateCompanyReq.setBizLicenseImg(req.getBizLicenseImg());
    addOrUpdateCompanyReq.setUploadType(req.getUploadType());

    Result<CompanyVO> addOrUpdateCompanyResult = companyMemberFeignService.addOrUpdateCompany(addOrUpdateCompanyReq);

    if (addOrUpdateCompanyResult == null || !addOrUpdateCompanyResult.isOk() || addOrUpdateCompanyResult.getData() == null) {
      throw new ServiceException(addOrUpdateCompanyResult != null ? addOrUpdateCompanyResult.getMsg() : "企业申请失败，请稍后重试");
    }

    if (addOrUpdateCompanyResult.getData().isSetAdminFlag()) {
      modCompanyApplyRecord.setRemark("管理员设置成功，请完成合同配置");
    } else {
      modCompanyApplyRecord.setRemark("请设置管理员并配置合同");
    }

    // 申请成功
    modCompanyApplyRecord.setApplyRes(1);
    modCompanyApplyRecord.setFailReason("");
    modCompanyApplyRecord.setEmail(req.getEmail() == null ? "" : req.getEmail());
    modCompanyApplyRecord.setEmailSuffix(req.getEmailSuffix() == null ? "" : req.getEmailSuffix());
    modCompanyApplyRecordMapper.insertOne(modCompanyApplyRecord);

    return addOrUpdateCompanyResult.getData();
  }

  private void handleCompanyApplyReq(CompanyApplyReq req) {
    req.setCompanyName(req.getCompanyName().trim());
    req.setTaxpayerId(req.getTaxpayerId().trim());
    req.setCompanyAddress(req.getCompanyAddress().trim());
    req.setLinkman(req.getLinkman().trim());
    req.setMobile(req.getMobile().trim());
    req.setUploadType(req.getUploadType() == null ? 0 : req.getUploadType());
  }


  private GetOrSaveEnterpriseReq transfer2PmsEnterprise(AddOrUpdateCompanyReq addOrUpdateCompanyReq, IssueUserResponse saleManInfo) {
    GetOrSaveEnterpriseReq req = new GetOrSaveEnterpriseReq();
    req.setAgreementEnterpriseType("ENTERPRISE_CUSTOMER");
    req.setDevelopSource("HOTEL");
    req.setAgreementEnterpriseMember(1);
    req.setEnableStatus(1);

    req.setHotelCode(addOrUpdateCompanyReq.getHotelCode());
    req.setAgreementEnterpriseName(addOrUpdateCompanyReq.getCompanyName());
    req.setAgreementEnterpriseAddr(addOrUpdateCompanyReq.getCompanyAddress());
    req.setTaxNumber(addOrUpdateCompanyReq.getTaxpayerId());
    req.setAgreementEnterpriseEmail(addOrUpdateCompanyReq.getEmailSuffix());

    req.setContactName(addOrUpdateCompanyReq.getLinkman());
    req.setContactPhone(addOrUpdateCompanyReq.getMobile());
    req.setContactEmail(addOrUpdateCompanyReq.getEmail());
    req.setSellerId(Long.parseLong(addOrUpdateCompanyReq.getSalesId()));
    req.setSellerName(saleManInfo.getName());
    req.setSellerPhone(saleManInfo.getMobile());
    return req;
  }

  @Override
  public PageInfo<ModCompanyApplyRecord> getCompanyApplyRecordPage(Integer pageNo, Integer pageSize) {
    Integer ucId = BaseThreadLocalHelper.getUser().getUcId();
    // 查询申请人所选酒店
    ModHotelInfo hotelInfo = hotelInfoService.queryById(BaseThreadLocalHelper.getCompanyId());
    String hotelCode = hotelInfo.getHotelCode();
    PageHelper.startPage(pageNo, pageSize);
    return new PageInfo<>(modCompanyApplyRecordMapper.selectByUcIdAndHotelCode(ucId, hotelCode));
  }


    @Override
    public CompanyContractRes queryContractByCompanyId(Long companyId) {
      // 获取当前酒店
      ModHotelInfo hotelInfo = hotelInfoService.queryById(BaseThreadLocalHelper.getCompanyId());
      if (hotelInfo == null) {
        throw new ServiceException("当前酒店不存在");
      }
      String hotelCode = hotelInfo.getHotelCode();
      // 调用接口查询企业合同信息
      CompanyContractVO companyContractVO = bdxRpcService.queryHotelContract(companyId, hotelCode);
      if (companyContractVO == null) {
        return null;
      }
      CompanyContractRes res = new CompanyContractRes();
      BeanUtil.copyProperties(companyContractVO, res);
      // 设置房价码信息（code、name）
      if (CollUtil.isNotEmpty(companyContractVO.getRateCodeList())) {
        List<CompanyContractRes.RoomRateInfo> roomRateInfoList = new ArrayList<>();
        // 查询房价码信息
        List<PricePlanHotelVO> rateInfoList = bdxRpcService.queryHotelRateCodeList(hotelCode);
        Map<String, String> rateInfoMap = rateInfoList.stream().collect(Collectors.toMap(PricePlanHotelVO::getTemplateCode, PricePlanHotelVO::getTemplateName));

        companyContractVO.getRateCodeList().forEach(rateCode -> {
          if (rateInfoMap.containsKey(rateCode)) {
            String rateName = rateInfoMap.get(rateCode);
            CompanyContractRes.RoomRateInfo roomRateInfo = new CompanyContractRes.RoomRateInfo();
            roomRateInfo.setRateCode(rateCode);
            roomRateInfo.setRateName(rateName);
            roomRateInfoList.add(roomRateInfo);
          }

        });

        res.setRoomRateInfoList(roomRateInfoList);
      }
      return res;
    }

  private void applyCompanyFail(ModCompanyApplyRecord modCompanyApplyRecord, String failReason) {
    modCompanyApplyRecord.setApplyRes(0);
    modCompanyApplyRecord.setFailReason(failReason);
    modCompanyApplyRecord.setRemark("");
    modCompanyApplyRecord.setEmail(modCompanyApplyRecord.getEmail() == null ? "" : modCompanyApplyRecord.getEmail());
    modCompanyApplyRecord.setEmailSuffix(modCompanyApplyRecord.getEmailSuffix() == null ? "" : modCompanyApplyRecord.getEmailSuffix());
    modCompanyApplyRecordMapper.insertOne(modCompanyApplyRecord);
    log.error("企业申请失败 modCompanyApplyRecord:{}", modCompanyApplyRecord);
    throw new ServiceException(failReason);
  }

  private void checkFile(MultipartFile file) {
    if (file == null) {
      throw new IllegalArgumentException("文件不能为空");
    }
    if (file.getSize() > 1024 * 1024 * 5) {
      throw new IllegalArgumentException("文件大小不能超过5MB");
    }
    // 文件类型
    String type = Objects.requireNonNull(file.getOriginalFilename())
        .substring(file.getOriginalFilename().lastIndexOf("."));
    if (!(".jpg".equalsIgnoreCase(type) || ".png".equalsIgnoreCase(type) || ".jpeg".equalsIgnoreCase(type))) {
      throw new IllegalArgumentException("文件类型不支持");
    }
  }

  @Override
  public List<RateCodeRes> queryRateCodeList() {
    // 获取当前酒店信息
    ModHotelInfo hotelInfo = hotelInfoService.queryById(BaseThreadLocalHelper.getCompanyId());
    if (hotelInfo == null) {
      throw new ServiceException("酒店信息不存在");
    }

    // 调用 BdxRpcService 获取房价码列表
    List<PricePlanHotelVO> pricePlanList = bdxRpcService.queryHotelRateCodeList(hotelInfo.getHotelCode());

    // 转换为响应对象
    List<RateCodeRes> rateCodeResList = new ArrayList<>();
    if (CollUtil.isNotEmpty(pricePlanList)) {
      for (PricePlanHotelVO pricePlan : pricePlanList) {
        RateCodeRes rateCodeRes = new RateCodeRes();
        rateCodeRes.setRateCode(pricePlan.getRatecode());
        rateCodeRes.setRateName(pricePlan.getTemplateName());
        rateCodeResList.add(rateCodeRes);
      }
    }

    return rateCodeResList;
  }

  @Override
  public String contractAttachmentUpload(MultipartFile file) {
    // 校验文件
    checkFile(file);
    log.info("上传合同附件 size:{} fileName:{} originalFileName:{} contentType: {}",
        file.getSize(), file.getName(), file.getOriginalFilename(), file.getContentType());
    // 上传
    String imageUrl = ossService.uploadContractAttachment(file);
    if (StrUtil.isBlank(imageUrl)) {
      throw new ServiceException("上传失败");
    }
    return imageUrl;
  }

  /**
   * 1. 企业名称限制，禁止包含下面字段（机构类型为酒店人员有限制，集团不做限制）
   *   1. 旅行社
   *   2. 旅游公司
   *   3. 旅游运营
   *   4. 旅游服务
   */
  public static boolean checkCompanyName(String companyName) {
    // 检查企业名称是否带有限制字段
    String exist = Arrays.stream(illegalCompanyNames).filter(companyName::contains).findAny().orElse(null);
    return exist == null;
  }
}
