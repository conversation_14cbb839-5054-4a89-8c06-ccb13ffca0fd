package com.shands.mod.main.remote.roomPrice;

import com.betterwood.base.common.model.PageVO;
import com.betterwood.base.common.model.Result;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.shands.mod.config.FeignConfigure;
import com.shands.mod.main.remote.roomPrice.fallback.RoomPriceRemoteServiceFallBack;
import com.shands.mod.main.service.betterwood.rpc.resp.PricePlanHotelVO;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "room-price-web", fallback = RoomPriceRemoteServiceFallBack.class, configuration = FeignConfigure.class)
public interface RoomPriceRemoteService {

    @GetMapping("/room_price/pr/priceplanhotel/page")
    @ApiOperation(value = "分页", notes = "集团、酒店房价计划分页查询")
    Result<PageVO<PricePlanHotelVO>> page(@RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
                                          @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
                                          @RequestParam(value = "codeName", required = false) String codeName,
                                          @RequestParam(value = "templateSrc") String templateSrc,
                                          @RequestParam(value = "isHalt", required = false) String isHalt,
                                          @RequestParam(value = "hotelCode", required = false) String hotelCode,
                                          @RequestParam(value = "newData ", required = false) String newData,
                                          @RequestParam(value = "isNormal", required = false) String isNormal,
                                          @ApiParam(value = "销售对象") @RequestParam(value = "salesObj", required = false) String salesObj,
                                          @ApiParam(value = "房价设置方式，T基准价F手动") @RequestParam(value = "useBasePrice", required = false) String useBasePrice,
                                          @ApiParam(value = "上线渠道") @RequestParam(value = "onlineChannel", required = false) String onlineChannel,
                                          @ApiParam(value = "事业部") @RequestParam(value = "deptCode", required = false) String deptCode);

}
