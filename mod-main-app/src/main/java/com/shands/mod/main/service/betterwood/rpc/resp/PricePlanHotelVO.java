package com.shands.mod.main.service.betterwood.rpc.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;

/**
 * 酒店、集团房价计划返回VO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "PricePlanHotelVO对象", description = "酒店、集团房价计划返回VO")
public class PricePlanHotelVO implements Serializable {
    private static final long serialVersionUID = 12389127931251213L;

    /**
     * 是否长期有效
     */
    @ApiModelProperty(value = "是否长期 长期有效为T 不是长期有效为F")
    private String isValid;
    /**
     * 是否标准库
     */
    @ApiModelProperty(value = "是否标准库  标准库为T ，不是标准为F")
    private String isNormal;
    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private LocalDate dateBegin;
    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private LocalDate dateEnd;

    @ApiModelProperty(value = "最小提前")
    private Integer advMin;

    @ApiModelProperty(value = "最大提前")
    private Integer advMax;

    @ApiModelProperty(value = "最小入住天数")
    private Integer stayMin;

    @ApiModelProperty(value = "最大入住天数")
    private Integer stayMax;

    /**
     * 房型大类
     */
    @ApiModelProperty(value = "房型大类")
    private String roomMainType;

    /**
     * 市场码
     */
    @ApiModelProperty(value = "市场码")
    private String market;

    @ApiModelProperty(value = "模板代码")
    private String templateCode;
    @ApiModelProperty(value = "模板名称")
    private String templateName;
    @ApiModelProperty(value = "模板名称（英文）")
    private String templateNameEn;
    @ApiModelProperty(value = "描述")
    private String descript;
    @ApiModelProperty(value = "模板归属：GROUP集团/HOTEL酒店")
    private String templateSrc;
    @ApiModelProperty(value = "模板类型：DIRECT(直连)/UNDIRECT(非直连)/REFERENCE(引用)")
    private String templateType;
    @ApiModelProperty(value = "价格类型")
    private String priceType;
    @ApiModelProperty(value = "房价保密")
    private String secretRateFlag;
    @ApiModelProperty(value = "固定房价 T:固定房价 F:不固定")
    private String isFxRate;
    @ApiModelProperty(value = "是否钟点房房价码")
    private String isHourRatecode;
    @ApiModelProperty(value = "钟点房小时数")
    private Integer hours;
    @ApiModelProperty(value = "直连模式下，是否允许区分底卖价")
    private String isDiffPrice;
    @ApiModelProperty(value = "直连模式下是否需要同步房价")
    private String needSync;
    @ApiModelProperty(value = "模板类型为DIRECT时起效")
    private String ratecode;
    @ApiModelProperty(value = "是否底价卖价一致，模板类型为UNDIRECT时生效")
    private String floorEqualsSale;
    @ApiModelProperty(value = "是否区分周末，模板类型为UNDIRECT时生效")
    private String weekDiff;
    @ApiModelProperty(value = "引用模板,模板类型为REFERENCE时起效")
    private String templateFrom;
    @ApiModelProperty(value = "乘法系数,模板类型为REFERENCE时生效")
    private BigDecimal mutiplyValue;
    @ApiModelProperty(value = "加法系数,模板类型为REFERENCE时生效")
    private BigDecimal addValue;
    @ApiModelProperty(value = "加法系数,模板类型为REFERENCE时生效")
    private BigDecimal divideValue;
    @ApiModelProperty(value = "小数位数,模板类型为REFERENCE时生效")
    private Integer decimalDigits;
    @ApiModelProperty(value = "进位规则,模板类型为REFERENCE时生效")
    private String carryType;


    @ApiModelProperty(value = "")
    private String unitCode;
    @ApiModelProperty(value = "")
    private String hotelGroupCode;
    @ApiModelProperty(value = "")
    private String hotelCode;

    @ApiModelProperty(value = "是否是新数据")
    private String newData;
    @ApiModelProperty(value = "尾数系数")
    private Integer mantissaValue;
    @ApiModelProperty(value = "尾数规则")
    private Integer mantissaRule;

    @ApiModelProperty(value = "排序")
    private Integer listOrder;
    @ApiModelProperty(value = "是否可用")
    private String isHalt;

    @ApiModelProperty(value = "id")
    private String id;
    @ApiModelProperty(value = "创建用户")
    private String createUser;
    @ApiModelProperty(value = "创建时间")
    private Date createDatetime;
    @ApiModelProperty(value = "修改用户")
    private String modifyUser;
    @ApiModelProperty(value = "修改时间")
    private Date modifyDatetime;

    @ApiModelProperty(value = "房型大类名称")
    private String roomMainName;

    @ApiModelProperty(value = "市场码名称")
    private String marketName;

    /**基准价版本新加字段*/
    @ApiModelProperty(value = "T基准价，默认F不使用基准价")
    private String useBasePrice;

    @ApiModelProperty(value = "是否长期销售 T,F")
    private String isLongSalesTime;
    @ApiModelProperty(value = "销售时间")
    private String customSalesTime;
    @ApiModelProperty(value = "销售对象")
    private String salesObj;
    @ApiModelProperty(value = "销售信息")
    private String salesInfo;

    @ApiModelProperty(value = "事业部")
    private String deptCode;
}
