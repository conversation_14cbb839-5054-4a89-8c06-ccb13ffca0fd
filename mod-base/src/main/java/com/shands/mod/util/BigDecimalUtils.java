package com.shands.mod.util;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023-03-27
 */
public class BigDecimalUtils {

  /**
   * BigDecimal：100
   */
  private static final BigDecimal BIG_DECIMAL_100 = BigDecimal.valueOf(100);

  /**
   * 整数：0，用作对比
   */
  private static final int INTEGER_ZERO = 0;

  /**
   * 小数保留2位
   */
  public static final int ROUNDING_SCALE_2 = 2;

  /**
   * 小数保留4位
   */
  public static final int ROUNDING_SCALE_4 = 4;

  /**
   * 计算比例：newVal/oldVal 计算结果为百分比保留两位小数的形式
   * 如：实际比例为「12.83%」，那么这里返回「12.83」
   *
   * @param newVal 分子
   * @param oldVal 分母
   * @return 百分比
   */
  public static String calculatePercentage(String newVal, String oldVal) {
    // 规范计算规则：任意计算值为空
    if (Objects.isNull(newVal) || Objects.isNull(oldVal)) {
      return BigDecimal.ZERO.toString();
    }
    return calculatePercentage(new BigDecimal(newVal), new BigDecimal(oldVal)).toString();
  }

  /**
   * 计算比例：newVal/oldVal 计算结果为百分比保留两位小数的形式
   * 如：实际比例为「12.83%」，那么这里返回「12.83」
   *
   * @param newVal 分子
   * @param oldVal 分母
   * @return 百分比
   */
  public static BigDecimal calculatePercentage(BigDecimal newVal, BigDecimal oldVal) {
    // 规范计算规则：任意计算值为空 || 分母为零，返回0
    if (Objects.isNull(newVal) || Objects.isNull(oldVal) || BigDecimal.ZERO.compareTo(oldVal) == INTEGER_ZERO) {
      return BigDecimal.ZERO;
    }
    return newVal.divide(oldVal, ROUNDING_SCALE_4, RoundingMode.HALF_UP)
        .multiply(BIG_DECIMAL_100)
        .setScale(ROUNDING_SCALE_2, RoundingMode.HALF_UP);
  }

  /**
   * 计算增长率：(newVal-oldVal)/oldVal 计算结果为百分比保留两位小数的形式
   * 如：实际比例为「12.83%」，那么这里返回「12.83」
   *
   * @param newVal 新值Str
   * @param oldVal 旧值Str
   * @return 增长率Str
   */
  public static String calculateIncreaseRate(String newVal, String oldVal) {
    // 规范计算规则：任意计算值为空
    if (Objects.isNull(newVal) || Objects.isNull(oldVal)) {
      return BigDecimal.ZERO.toString();
    }
    return calculateIncreaseRate(new BigDecimal(newVal), new BigDecimal(oldVal)).toString();
  }

  /**
   * 计算增长率：(newVal-oldVal)/oldVal 计算结果为百分比保留两位小数的形式
   * 如：实际比例为「12.83%」，那么这里返回「12.83」
   *
   * @param newVal 新值
   * @param oldVal 旧值
   * @return 增长率
   */
  public static BigDecimal calculateIncreaseRate(BigDecimal newVal, BigDecimal oldVal) {
    // 规范计算规则：任意计算值为空 || 分母为零，返回0
    if (Objects.isNull(newVal) || Objects.isNull(oldVal) || BigDecimal.ZERO.compareTo(oldVal) == INTEGER_ZERO) {
      return BigDecimal.ZERO;
    }
    return newVal.subtract(oldVal).divide(oldVal, ROUNDING_SCALE_4, RoundingMode.HALF_UP)
        .multiply(BIG_DECIMAL_100)
        .setScale(ROUNDING_SCALE_2, RoundingMode.HALF_UP);
  }

  /**
   * 两数相加
   * @param firstVal 第一个值
   * @param secondVal 第二值
   * @return 相加后的结果
   */
  public static BigDecimal calculateAdd(BigDecimal firstVal, BigDecimal secondVal) {
    if (Objects.isNull(firstVal) && Objects.isNull(secondVal)) {
      return BigDecimal.ZERO;
    } else if (Objects.isNull(firstVal) ) {
      return secondVal;
    } else if (Objects.isNull(secondVal)) {
      return firstVal;
    }
    return firstVal.add(secondVal);
  }
}
