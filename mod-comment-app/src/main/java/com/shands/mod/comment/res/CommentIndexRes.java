package com.shands.mod.comment.res;

import com.shands.mod.comment.vo.IndexVO;
import com.shands.mod.dao.model.wp.CommentReportNewScoreVo;
import com.shands.mod.dao.model.wp.CommentReportTrendVo;
import java.util.List;
import java.util.Map;
import com.shands.mod.dao.model.wp.CommentRoomCommentVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/31 16:47
 */
@Data
public class CommentIndexRes {

  IndexVO indexVO;
  @ApiModelProperty(value = "单房网评量")
  CommentRoomCommentVo commentRoomCommentVo;
  List<CommentReportTrendVo> commentReportTrendVoList;
  List<IndexVO> indexVOList;
  @ApiModelProperty(value = "酒店报表")
  Map<String, CommentReportNewScoreVo> roomReport;
}
