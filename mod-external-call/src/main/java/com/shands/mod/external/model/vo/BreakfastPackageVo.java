package com.shands.mod.external.model.vo;

import lombok.Data;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021-06-10
 * @description
 */
@Data
public class BreakfastPackageVo {

  /**
   * 业务日期
   */
  private Date businessDate;
  /**
   * 房间号
   */
  private String roomNo;
  /**
   * 宾客姓名
   */
  private String guestName;
  /**
   * 订单号
   */
  private String confirmationNo;
  /**
   * 入住日期
   */
  private Date truncBeginDate;
  /**
   * 离店日期
   */
  private Date truncEndDate;
  /**
   * 房型代码
   */
  private String roomType;
  /**
   * 房型名称
   */
  private String roomTypeName;
  /**
   * 成年数
   */
  private String adults;
  /**
   * 儿童数
   */
  private String children;
  /**
   * 包价代码
   */
  private String product;
  /**
   * 包价代码描述
   */
  private String productShortDesc;
}
