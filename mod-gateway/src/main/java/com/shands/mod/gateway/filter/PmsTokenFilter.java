package com.shands.mod.gateway.filter;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shands.mod.gateway.util.RedisUtils;
import com.shands.mod.gateway.util.Tools;
import com.shands.mod.gateway.vo.ResultVO;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.cloud.gateway.route.Route;
import org.springframework.cloud.gateway.support.ServerWebExchangeUtils;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

/**
 * pms token过滤器
 *
 * <AUTHOR>
 */
@Component
@RefreshScope
public class PmsTokenFilter implements GlobalFilter {

  private static final Logger LOG = LoggerFactory.getLogger(PmsTokenFilter.class);

  private static final Long PMS_TOKEN_EXPIRATION = 43200L;

  private static final Long PMS_TOKEN_REDIS_EXPIRATION = 3600L;

  private static final String PMS_ROUTE_ID = "mod-pms";

  @Autowired
  private RedisTemplate redisTemplate;

  @Autowired
  private RedisUtils redisUtils;

  @Value("${shands.url:http://dev-mod-main-app.betterwood.com/main/}")
  private String shandsUrl;


  @Override
  public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
    Route route = (Route) exchange.getAttributes().get(ServerWebExchangeUtils.GATEWAY_ROUTE_ATTR);
    String id = route.getId();
    if (!PMS_ROUTE_ID.equals(id)) {
      return chain.filter(exchange);
    }

    ServerHttpRequest request = exchange.getRequest();
    String token = Tools.getToken(request);
    if (StringUtils.isEmpty(token)) {
      return this.forbiddenError(exchange);
    }

    String hotelCode = Tools.getHotelCode(request);

    String key = Tools.buildKey(BaseConstants.CACHE_USER, token);
    Object obj = this.redisTemplate.opsForValue().get(key);
    if (null == obj) {
      return this.forbiddenError(exchange);
    }
    JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(obj));
    String mobile = jsonObject.getString("mobile");

    //通过德胧生态token获取pms token
    String pmsToken = getPmsToken(mobile, token, hotelCode);
    if (StringUtils.isEmpty(pmsToken)) {
      return this.forbiddenError(exchange);
    }
    //查询pmsToken绑定的酒店是否和header一致
    String pmsTokenHotelKey = Tools.buildKey(BaseConstants.CACHE_USER_PMS_HOTEL, pmsToken);
    Object pmsTokenHotelObj = this.redisTemplate.opsForValue().get(pmsTokenHotelKey);
    if (null == pmsTokenHotelObj || !pmsTokenHotelObj.equals(hotelCode)) {
      bindPmsTokenHotel(pmsToken, hotelCode);
    }
    //6)组装子应用请求头
    ServerHttpRequest newRequest = exchange.getRequest().mutate()
        .header(BaseConstants.HEADER_TOKEN, token)
        .header("uc-token", pmsToken)
        .header("x-pms-source", "shands")
        .header("uc-application", "pms-service")
        .build();

    return chain.filter(exchange.mutate().request(newRequest).build());
  }


  private Mono<Void> forbiddenError(ServerWebExchange exchange) {
    ResultVO<Object> bean = ResultVO.failed("禁止访问");
    return error(exchange, bean);
  }


  /**
   * 返回错误信息
   *
   * @param exchange
   * @param result
   * @return
   */
  private Mono<Void> error(ServerWebExchange exchange, ResultVO<Object> result) {
    ServerHttpResponse response = exchange.getResponse();
    byte[] bits = JSON.toJSONString(result).getBytes(StandardCharsets.UTF_8);
    DataBuffer buffer = response.bufferFactory().wrap(bits);
    response.setStatusCode(HttpStatus.OK);
    response.getHeaders().add(BaseConstants.HEADER_CONTENT_TYPE, BaseConstants.CONTENT_TYPE_JSON);
    return response.writeWith(Mono.just(buffer));
  }


  private String getPmsToken(String mobile, String token, String hotelCode) {
    String pmsTokenKey = Tools.buildKey(BaseConstants.CACHE_PMS_TOKEN, mobile);
    Object pmsTokenObj = this.redisTemplate.opsForValue().get(pmsTokenKey);
    String pmsToken = null;
    if (null == pmsTokenObj) {
      //如果redis中不存在，通过分布式锁获取token
      String lockKey = Tools.buildKey(BaseConstants.CACHE_PMS_TOKEN_LOCK, mobile);
      if (redisUtils.tryLock(lockKey, 6000L, 6000L, 100L)) {
        try {
          //再从redis中获取一次
          pmsTokenObj = this.redisTemplate.opsForValue().get(pmsTokenKey);
          if (null == pmsTokenObj) {
            //获取pmsToken
            LOG.info("获取pmsToken,mobile:{}" , mobile);
            pmsToken = getPmsToken(token, hotelCode);
            if (StringUtils.isEmpty(pmsToken)) {
              return null;
            }
            this.redisTemplate.opsForValue()
                .set(pmsTokenKey, pmsToken, PMS_TOKEN_REDIS_EXPIRATION, TimeUnit.SECONDS);
          } else {
            pmsToken = (String) pmsTokenObj;
          }
        } finally {
          redisUtils.delete(lockKey);
        }
      }
    } else {
      pmsToken = (String) pmsTokenObj;
    }
    return pmsToken;
  }


  private String getPmsToken(String token, String hotelCode) {
    try {
      String url = shandsUrl + "/token/pmsToken";
      Map<String, String> header = new HashMap<>();
      header.put("token", token);
      String res = HttpRequest.get(url)
          .addHeaders(header)
          .form("token", token)
          .form("hotelCode", hotelCode)
          .form("expiration", PMS_TOKEN_EXPIRATION)
          .execute()
          .body();
      LOG.info("获取pmsToken, token:{}, res:{}", token, JSON.toJSONString(res));
      return Optional.ofNullable(res)
          .map(JSON::parseObject)
          .map(jo -> jo.getJSONObject("data").getString("pmsToken")).get();
    } catch (Exception ex) {
      LOG.error("获取pmsToken失败, token:" + JSON.toJSONString(token), ex);
      return null;
    }
  }

  private boolean bindPmsTokenHotel(String pmsToken, String hotelCode) {
    try {
      String url = shandsUrl + "/token/pmsToken/hotel/bind";
      Map<String, String> header = new HashMap<>();
      Map<String, Object> paramMap = new HashMap<>();
      paramMap.put("pmsToken", pmsToken);
      paramMap.put("hotelCode", hotelCode);
      String res = HttpRequest.post(url)
          .addHeaders(header)
          .body(JSONObject.toJSONString(paramMap))
          .execute()
          .body();
      LOG.info("绑定pmsToken, pmsToken:{},hotelCode:{}, res:{}", pmsToken, hotelCode,
          JSON.toJSONString(res));
      return Optional.ofNullable(res)
          .map(JSON::parseObject)
          .map(jo -> jo.getBoolean("data")).get();
    } catch (Exception ex) {
      LOG.error("绑定pmsToken失败, pmsToken:" + JSON.toJSONString(pmsToken), ex);
      return false;
    }

  }


}